@echo off
echo ========================================
echo Création du package SWAP v1.5.1
echo ========================================
echo.

REM Créer le dossier de package
set PACKAGE_DIR=SWAP_v1.5.1_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

REM Copier l'exécutable principal
echo Copie de l'exécutable principal...
copy "dist\SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe" "%PACKAGE_DIR%\"

REM Copier les fichiers de données nécessaires
echo Copie des fichiers de données...
xcopy "data" "%PACKAGE_DIR%\data" /E /I /Y
copy "SumatraPDF.exe" "%PACKAGE_DIR%\"
copy ".env" "%PACKAGE_DIR%\" 2>nul

REM Créer le script de lancement
echo Création du script de lancement...
echo @echo off > "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo echo SWAP v1.5.1 - Impression Corrigée >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo echo Démarrage de l'application... >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo if errorlevel 1 ( >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo     echo ERREUR: Impossible de démarrer l'application >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo     pause >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"
echo ^) >> "%PACKAGE_DIR%\run_SWAP_v1.5.1.bat"

REM Créer le script de vérification
echo Création du script de vérification...
echo @echo off > "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo SWAP v1.5.1 - Vérification du système >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo Vérification des imprimantes disponibles... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo echo Vérification terminée. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"
echo pause >> "%PACKAGE_DIR%\VERIFICATION_v1.5.1.bat"

REM Créer le fichier d'information
echo Création du fichier d'information...
echo SWAP Version 1.5.1 - Impression Corrigée > "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ======================================== >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo Date de création: %date% %time% >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo CORRECTIONS APPORTÉES: >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ====================== >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ Problème d'impression résolu >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ Logique intelligente de sélection d'imprimante >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ Priorité 1: HP LaserJet M109-M112 >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ Priorité 2: EPSON ET contenant 2810 >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ Priorité 3: Imprimante par défaut (si non-POSTEK) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ✓ POSTEK réservé uniquement aux étiquettes outbound >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ============= >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo 1. Copier tout le dossier SWAP_v1.5.1_Package sur le PC cible >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo 2. Exécuter VERIFICATION_v1.5.1.bat pour vérifier les imprimantes >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.5.1.bat >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo FICHIERS INCLUS: >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo ================ >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo - SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe (Application principale) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo - SumatraPDF.exe (Visualiseur PDF) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo - data\ (Dossier contenant les ressources) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo - run_SWAP_v1.5.1.bat (Script de lancement) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"
echo - VERIFICATION_v1.5.1.bat (Vérification système) >> "%PACKAGE_DIR%\VERSION_1.5.1_INFO.txt"

REM Créer l'archive ZIP
echo Création de l'archive ZIP...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath 'SWAP_v1.5.1_IMPRESSION_CORRIGEE.zip' -Force"

echo.
echo ========================================
echo ✓ PACKAGE CRÉÉ AVEC SUCCÈS !
echo ========================================
echo.
echo Dossier: %PACKAGE_DIR%
echo Archive: SWAP_v1.5.1_IMPRESSION_CORRIGEE.zip
echo.
echo Le package est prêt pour le déploiement !
echo.
