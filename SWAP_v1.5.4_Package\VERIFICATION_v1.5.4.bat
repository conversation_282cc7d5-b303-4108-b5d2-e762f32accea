@echo off 
echo ======================================== 
echo SWAP v1.5.4 - Vérification du système 
echo ======================================== 
echo. 
echo Vérification des imprimantes disponibles... 
python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" 
echo. 
echo Vérification terminée. 
pause 
