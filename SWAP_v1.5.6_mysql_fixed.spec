# -*- mode: python ; coding: utf-8 -*- 
 
block_cipher = None 
 
a = Analysis( 
    ['app.py'], 
    pathex=[], 
    binaries=[], 
    datas=[ 
        ('data', 'data'), 
        ('fix_mysql_locale_error.py', '.' ) 
    ], 
    hiddenimports=[ 
        'mysql.connector', 
        'mysql.connector.locales', 
        'mysql.connector.locales.eng', 
        'win32api', 
        'win32print', 
        'win32gui', 
        'win32con', 
        'reportlab.pdfgen', 
        'reportlab.lib.pagesizes', 
        'wkhtmltopdf', 
        'tkinter', 
        'tkinter.messagebox', 
        'tkinter.ttk', 
        'locale', 
        'os' 
    ], 
    hookspath=[], 
    hooksconfig={}, 
    runtime_hooks=[], 
    excludes=[ 
        'ghostscript', 
        'gsprint', 
        'gswin32', 
        'gsdll32' 
    ], 
    win_no_prefer_redirects=False, 
    win_private_assemblies=False, 
    cipher=block_cipher, 
    noarchive=False, 
) 
 
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher) 
 
exe = EXE( 
    pyz, 
    a.scripts, 
    a.binaries, 
    a.zipfiles, 
    a.datas, 
    [], 
    name='SWAP_v1.5.6_MYSQL_FIXED', 
    debug=False, 
    bootloader_ignore_signals=False, 
    strip=False, 
    upx=True, 
    upx_exclude=[], 
    console=False, 
    disable_windowed_traceback=False, 
    target_arch=None, 
    codesign_identity=None, 
    entitlements_file=None, 
    icon='data/exchange.ico' 
) 
