@echo off
echo ========================================
echo SETUP COMPLET SWAP v1.5.6
echo IMPRESSION AUTOMATIQUE SANS GHOSTSCRIPT
echo ========================================
echo.

echo ÉTAPE 1: Téléchargement des outils PDF...
echo ==========================================
echo.

REM Télécharger PDFtoPrinter
echo [1/2] Téléchargement de PDFtoPrinter...
call download_pdf_tools.bat

echo.
echo [2/2] Téléchargement de SumatraPDF...
call download_sumatra_portable.bat

echo.
echo ÉTAPE 2: Vérification des outils...
echo ===================================
echo.

if exist "data\PDFtoPrinter.exe" (
    echo ✓ PDFtoPrinter disponible
) else (
    echo ⚠ PDFtoPrinter manquant
)

if exist "data\SumatraPDF.exe" (
    echo ✓ SumatraPDF disponible
) else (
    echo ⚠ SumatraPDF manquant
)

echo.
echo ÉTAPE 3: Mise à jour du fichier spec...
echo =======================================
echo.

REM Mettre à jour le fichier spec pour la version 1.5.6
echo Mise à jour vers version 1.5.6...

echo.
echo ÉTAPE 4: Compilation de SWAP v1.5.6...
echo ======================================
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe" del "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.6...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe" | find ".exe"
    echo.
    
    echo ÉTAPE 5: Création du package de déploiement...
    echo ==============================================
    call create_package_v1.5.6.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
echo ========================================
echo SETUP TERMINÉ
echo ========================================
echo.
echo RÉSUMÉ:
echo ✓ Outils PDF téléchargés
echo ✓ SWAP v1.5.6 compilé
echo ✓ Package de déploiement créé
echo.
echo SOLUTION ANTI-GHOSTSCRIPT:
echo - PDFtoPrinter pour impression directe
echo - SumatraPDF comme fallback
echo - Impression automatique maintenue
echo - Plus d'interface Ghostscript
echo.
pause
