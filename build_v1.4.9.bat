@echo off
echo ========================================
echo SWAP Version 1.4.9 - Build Script
echo Correction d'impression intelligente
echo ========================================

echo.
echo Verification de l'environnement...

REM Verifier si PyInstaller est installe
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ERREUR: PyInstaller n'est pas installe!
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo PyInstaller detecte ✓

REM Verifier les dependances principales
echo Verification des dependances...
python -c "import mysql.connector, pdfkit, win32print, win32api, PIL, pandas, numpy, tkinter" 2>nul
if errorlevel 1 (
    echo ERREUR: Certaines dependances sont manquantes!
    echo Installation des dependances...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer les dependances
        pause
        exit /b 1
    )
)

echo Dependances verifiees ✓

REM Nettoyer les anciens builds
echo.
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "app.build" rmdir /s /q "app.build"
if exist "app.dist" rmdir /s /q "app.dist"
if exist "app.onefile-build" rmdir /s /q "app.onefile-build"

echo Nettoyage termine ✓

REM Compiler l'application
echo.
echo ========================================
echo Compilation de SWAP v1.4.9...
echo ========================================

pyinstaller SWAP_v1.4.9.spec

if errorlevel 1 (
    echo.
    echo ERREUR: La compilation a echoue!
    echo Verifiez les erreurs ci-dessus.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Compilation terminee avec succes! ✓
echo ========================================

REM Verifier que l'executable a ete cree
if exist "dist\SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe" (
    echo.
    echo Executable cree: dist\SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe" | find ".exe"
) else (
    echo.
    echo ERREUR: L'executable n'a pas ete trouve!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build termine avec succes!
echo ========================================
echo.
echo L'executable se trouve dans: dist\SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
