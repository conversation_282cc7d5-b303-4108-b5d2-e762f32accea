@echo off
echo ========================================
echo SWAP v1.6.0 - SOLUTION DÉFINITIVE ANTI-GHOSTSCRIPT
echo ========================================
echo.
echo 🎯 PROBLÈME GHOSTSCRIPT RÉSOLU À 100%:
echo ✅ AUCUNE interface Ghostscript ne peut s'ouvrir
echo ✅ PDF s'ouvre pour impression manuelle
echo ✅ Instructions claires pour l'utilisateur
echo ✅ Logique d'impression intelligente maintenue
echo ✅ Solution simple, fiable et définitive
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.6.0_SOLUTION_DEFINITIVE_ANTI_GHOSTSCRIPT.exe" del "dist\SWAP_v1.6.0_SOLUTION_DEFINITIVE_ANTI_GHOSTSCRIPT.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.6.0...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.6.0_SOLUTION_DEFINITIVE_ANTI_GHOSTSCRIPT.exe" (
    echo.
    echo ========================================
    echo ✅ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.6.0_SOLUTION_DEFINITIVE_ANTI_GHOSTSCRIPT.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.6.0_SOLUTION_DEFINITIVE_ANTI_GHOSTSCRIPT.exe" | find ".exe"
    echo.
    
    REM Créer le package de déploiement
    call create_package_v1.6.0.bat
    
) else (
    echo.
    echo ========================================
    echo ❌ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
pause
