from cabarchive.file import CabFile as CabFile
from cabarchive.parser import CabArchiveParser as CabArchiveParser
from cabarchive.writer import CabArchiveWriter as CabArchiveWriter
from typing import List, Optional

class CabArchive(dict):
    set_id: int
    def __init__(self, buf: Optional[bytes] = ..., flattern: bool = ...) -> None: ...
    def __setitem__(self, key: str, val: CabFile) -> None: ...
    def parse(self, buf: bytes) -> None: ...
    def find_file(self, glob: str) -> Optional[CabFile]: ...
    def find_files(self, glob: str) -> List[CabFile]: ...
    def save(self, compress: bool = ..., sort: bool = ...) -> bytes: ...
