@echo off
echo ========================================
echo Téléchargement de SumatraPDF Portable
echo ========================================
echo.

REM Créer le dossier data s'il n'existe pas
if not exist "data" mkdir "data"

echo Téléchargement de SumatraPDF Portable...
echo.
echo SumatraPDF est un lecteur PDF léger qui peut imprimer
echo sans déclencher Ghostscript dans certains cas.
echo.

REM Vérifier si curl est disponible
curl --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: curl n'est pas disponible
    echo.
    echo SOLUTION MANUELLE:
    echo 1. Aller sur: https://www.sumatrapdfreader.org/download-free-pdf-viewer
    echo 2. Télécharger la version portable
    echo 3. Extraire SumatraPDF.exe dans le dossier data\
    echo.
    pause
    exit /b 1
)

REM Télécharger SumatraPDF portable
echo Téléchargement en cours...
curl -L -o "data\SumatraPDF-portable.zip" "https://files.sumatrapdfreader.org/file/kjk-files/software/sumatrapdf/rel/SumatraPDF-3.4.6.zip"

if exist "data\SumatraPDF-portable.zip" (
    echo ✓ SumatraPDF téléchargé avec succès
    echo.
    echo Extraction...
    
    REM Extraire avec PowerShell
    powershell -command "Expand-Archive -Path 'data\SumatraPDF-portable.zip' -DestinationPath 'data\' -Force"
    
    REM Vérifier l'extraction
    if exist "data\SumatraPDF.exe" (
        echo ✓ SumatraPDF extrait avec succès
        del "data\SumatraPDF-portable.zip"
        echo.
        echo Test de SumatraPDF...
        "data\SumatraPDF.exe" -h
        echo.
        echo ✓ SumatraPDF est prêt à utiliser
    ) else (
        echo ⚠ Extraction échouée, vérifiez manuellement
    )
) else (
    echo ✗ Échec du téléchargement
    echo.
    echo SOLUTION ALTERNATIVE:
    echo Téléchargez manuellement SumatraPDF portable depuis:
    echo https://www.sumatrapdfreader.org/download-free-pdf-viewer
    echo Et placez SumatraPDF.exe dans le dossier data\
)

echo.
echo ========================================
echo Téléchargement terminé
echo ========================================
pause
