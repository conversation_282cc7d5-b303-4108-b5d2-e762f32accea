Metadata-Version: 2.1
Name: cabarchive
Version: 0.2.4
Summary: A pure-python library for creating and extracting cab files
Home-page: https://github.com/hughsie/python-cabarchive
Author: <PERSON>
Author-email: <EMAIL>
License: LGPL-2.1-or-later
Keywords: cabextract,cab,archive,extract
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
Classifier: Programming Language :: Python
Classifier: Topic :: Utilities
Classifier: Topic :: System :: Archiving
License-File: LICENSE


Contributors welcome, either adding new functionality or fixing bugs.

See also: https://msdn.microsoft.com/en-us/library/bb417343.aspx


