@echo off
echo ========================================
echo BUILD SWAP v1.5.6 - MYSQL LOCALE FIXED
echo SOLUTION ANTI-GHOSTSCRIPT + FIX MYSQL
echo ========================================
echo.

echo ÉTAPE 1: Vérification de l'environnement...
echo ==========================================
echo.

REM Vérifier Python
python --version
if errorlevel 1 (
    echo ERREUR: Python non trouvé
    pause
    exit /b 1
)

REM Vérifier PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo ✓ Environnement vérifié

echo.
echo ÉTAPE 2: Test du fix MySQL locale...
echo ===================================
echo.

echo Test de la correction MySQL...
python fix_mysql_locale_error.py
echo.

echo.
echo ÉTAPE 3: Nettoyage des anciens builds...
echo =======================================
echo.

REM Nettoyer les anciens builds
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.6_MYSQL_FIXED.exe" del "dist\SWAP_v1.5.6_MYSQL_FIXED.exe"

echo ✓ Nettoyage terminé

echo.
echo ÉTAPE 4: Création du fichier spec...
echo ===================================
echo.

REM Créer le fichier spec pour PyInstaller
echo # -*- mode: python ; coding: utf-8 -*- > SWAP_v1.5.6_mysql_fixed.spec
echo. >> SWAP_v1.5.6_mysql_fixed.spec
echo block_cipher = None >> SWAP_v1.5.6_mysql_fixed.spec
echo. >> SWAP_v1.5.6_mysql_fixed.spec
echo a = Analysis^( >> SWAP_v1.5.6_mysql_fixed.spec
echo     ['app.py'], >> SWAP_v1.5.6_mysql_fixed.spec
echo     pathex=[], >> SWAP_v1.5.6_mysql_fixed.spec
echo     binaries=[], >> SWAP_v1.5.6_mysql_fixed.spec
echo     datas=[ >> SWAP_v1.5.6_mysql_fixed.spec
echo         ^('data', 'data'^), >> SWAP_v1.5.6_mysql_fixed.spec
echo         ^('fix_mysql_locale_error.py', '.'^ ) >> SWAP_v1.5.6_mysql_fixed.spec
echo     ], >> SWAP_v1.5.6_mysql_fixed.spec
echo     hiddenimports=[ >> SWAP_v1.5.6_mysql_fixed.spec
echo         'mysql.connector', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'mysql.connector.locales', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'mysql.connector.locales.eng', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'win32api', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'win32print', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'win32gui', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'win32con', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'reportlab.pdfgen', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'reportlab.lib.pagesizes', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'wkhtmltopdf', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'tkinter', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'tkinter.messagebox', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'tkinter.ttk', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'locale', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'os' >> SWAP_v1.5.6_mysql_fixed.spec
echo     ], >> SWAP_v1.5.6_mysql_fixed.spec
echo     hookspath=[], >> SWAP_v1.5.6_mysql_fixed.spec
echo     hooksconfig={}, >> SWAP_v1.5.6_mysql_fixed.spec
echo     runtime_hooks=[], >> SWAP_v1.5.6_mysql_fixed.spec
echo     excludes=[ >> SWAP_v1.5.6_mysql_fixed.spec
echo         'ghostscript', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'gsprint', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'gswin32', >> SWAP_v1.5.6_mysql_fixed.spec
echo         'gsdll32' >> SWAP_v1.5.6_mysql_fixed.spec
echo     ], >> SWAP_v1.5.6_mysql_fixed.spec
echo     win_no_prefer_redirects=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     win_private_assemblies=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     cipher=block_cipher, >> SWAP_v1.5.6_mysql_fixed.spec
echo     noarchive=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo ^) >> SWAP_v1.5.6_mysql_fixed.spec
echo. >> SWAP_v1.5.6_mysql_fixed.spec
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^) >> SWAP_v1.5.6_mysql_fixed.spec
echo. >> SWAP_v1.5.6_mysql_fixed.spec
echo exe = EXE^( >> SWAP_v1.5.6_mysql_fixed.spec
echo     pyz, >> SWAP_v1.5.6_mysql_fixed.spec
echo     a.scripts, >> SWAP_v1.5.6_mysql_fixed.spec
echo     a.binaries, >> SWAP_v1.5.6_mysql_fixed.spec
echo     a.zipfiles, >> SWAP_v1.5.6_mysql_fixed.spec
echo     a.datas, >> SWAP_v1.5.6_mysql_fixed.spec
echo     [], >> SWAP_v1.5.6_mysql_fixed.spec
echo     name='SWAP_v1.5.6_MYSQL_FIXED', >> SWAP_v1.5.6_mysql_fixed.spec
echo     debug=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     bootloader_ignore_signals=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     strip=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     upx=True, >> SWAP_v1.5.6_mysql_fixed.spec
echo     upx_exclude=[], >> SWAP_v1.5.6_mysql_fixed.spec
echo     console=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     disable_windowed_traceback=False, >> SWAP_v1.5.6_mysql_fixed.spec
echo     target_arch=None, >> SWAP_v1.5.6_mysql_fixed.spec
echo     codesign_identity=None, >> SWAP_v1.5.6_mysql_fixed.spec
echo     entitlements_file=None, >> SWAP_v1.5.6_mysql_fixed.spec
echo     icon='data/exchange.ico' >> SWAP_v1.5.6_mysql_fixed.spec
echo ^) >> SWAP_v1.5.6_mysql_fixed.spec

echo ✓ Fichier spec créé avec fix MySQL

echo.
echo ÉTAPE 5: Compilation avec PyInstaller...
echo =======================================
echo.

pyinstaller SWAP_v1.5.6_mysql_fixed.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.6_MYSQL_FIXED.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.6_MYSQL_FIXED.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.6_MYSQL_FIXED.exe" | find ".exe"
    echo.
    
    echo ÉTAPE 6: Création du package de déploiement...
    echo ==============================================
    
    REM Créer le dossier de package
    if not exist "SWAP_v1.5.6_MySQL_Fixed_Package" mkdir "SWAP_v1.5.6_MySQL_Fixed_Package"
    
    REM Copier l'exécutable
    copy "dist\SWAP_v1.5.6_MYSQL_FIXED.exe" "SWAP_v1.5.6_MySQL_Fixed_Package\"
    
    REM Copier les fichiers de données
    if exist "data" xcopy "data" "SWAP_v1.5.6_MySQL_Fixed_Package\data" /E /I /Y
    
    REM Copier le fix MySQL
    copy "fix_mysql_locale_error.py" "SWAP_v1.5.6_MySQL_Fixed_Package\"
    
    REM Créer le script de lancement
    echo @echo off > "SWAP_v1.5.6_MySQL_Fixed_Package\run_SWAP_v1.5.6_FIXED.bat"
    echo echo Lancement de SWAP v1.5.6 MySQL Fixed... >> "SWAP_v1.5.6_MySQL_Fixed_Package\run_SWAP_v1.5.6_FIXED.bat"
    echo start SWAP_v1.5.6_MYSQL_FIXED.exe >> "SWAP_v1.5.6_MySQL_Fixed_Package\run_SWAP_v1.5.6_FIXED.bat"
    
    REM Créer le script de vérification
    echo @echo off > "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo VERIFICATION SWAP v1.5.6 MYSQL FIXED >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo Verification de l'executable... >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo if exist "SWAP_v1.5.6_MYSQL_FIXED.exe" ^( >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo     echo ✓ Executable present >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo ^) else ^( >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo     echo ✗ Executable manquant >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo ^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo Test du fix MySQL... >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo python fix_mysql_locale_error.py >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo CORRECTIONS APPLIQUEES >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ✓ Fix MySQL locale error >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ✓ Solution anti-Ghostscript >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ✓ Impression native Windows >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo echo ✓ Methodes de fallback multiples >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    echo pause >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERIFICATION_v1.5.6_FIXED.bat"
    
    REM Créer le fichier d'information
    echo SWAP v1.5.6 - MYSQL LOCALE FIXED > "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo ======================================== >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo PROBLEMES RESOLUS: >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo ✓ Interface Ghostscript ^(elimine^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo ✓ Erreur MySQL locale ^(corrige^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo ERREUR MYSQL CORRIGEE: >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo - No localization support for language 'eng' >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo SOLUTION APPLIQUEE: >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo - Configuration locale avant import MySQL >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo - Variables d'environnement configurees >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo - Locale C universelle utilisee >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo. >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo METHODES D'IMPRESSION: >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo 1. Adobe Reader ^(priorite 1^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo 2. win32api printto ^(fallback^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo 3. Commande Windows native >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    echo 4. Ouverture manuelle ^(dernier recours^) >> "SWAP_v1.5.6_MySQL_Fixed_Package\VERSION_1.5.6_FIXED_INFO.txt"
    
    REM Créer l'archive
    echo Création de l'archive...
    powershell Compress-Archive -Path "SWAP_v1.5.6_MySQL_Fixed_Package\*" -DestinationPath "SWAP_v1.5.6_MYSQL_LOCALE_FIXED.zip" -Force
    
    echo ✓ Package créé: SWAP_v1.5.6_MySQL_Fixed_Package
    echo ✓ Archive créée: SWAP_v1.5.6_MYSQL_LOCALE_FIXED.zip
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
echo ========================================
echo BUILD TERMINÉ
echo ========================================
echo.
echo RÉSUMÉ:
echo ✓ Fix MySQL locale error intégré
echo ✓ Solution anti-Ghostscript maintenue
echo ✓ Impression native Windows
echo ✓ Package de déploiement créé
echo.
echo ERREURS CORRIGÉES:
echo ✓ No localization support for language 'eng'
echo ✓ Interface Ghostscript éliminée
echo.
echo MÉTHODES D'IMPRESSION:
echo 1. Adobe Reader ^(priorité 1^)
echo 2. win32api printto ^(fallback^)
echo 3. Commande Windows native
echo 4. Ouverture manuelle ^(dernier recours^)
echo.
echo PLUS D'ERREUR MYSQL NI GHOSTSCRIPT !
echo.
pause
