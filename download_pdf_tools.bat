@echo off
echo ========================================
echo Téléchargement des outils PDF
echo ========================================
echo.

REM Créer le dossier data s'il n'existe pas
if not exist "data" mkdir "data"

echo Téléchargement de PDFtoPrinter...
echo.
echo PDFtoPrinter est un outil gratuit qui permet d'imprimer des PDF
echo sans déclencher Ghostscript. Il utilise l'API Windows directement.
echo.

REM Vérifier si curl est disponible
curl --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: curl n'est pas disponible
    echo.
    echo SOLUTION MANUELLE:
    echo 1. Aller sur: https://github.com/jzakiya/PDFtoPrinter/releases
    echo 2. Télécharger PDFtoPrinter.exe
    echo 3. Placer le fichier dans le dossier data\
    echo.
    pause
    exit /b 1
)

REM Télécharger PDFtoPrinter
echo Téléchargement en cours...
curl -L -o "data\PDFtoPrinter.exe" "https://github.com/jzakiya/PDFtoPrinter/releases/download/v1.7/PDFtoPrinter.exe"

if exist "data\PDFtoPrinter.exe" (
    echo ✓ PDFtoPrinter téléchargé avec succès
    echo.
    echo Test de PDFtoPrinter...
    "data\PDFtoPrinter.exe" /?
    echo.
    echo ✓ PDFtoPrinter est prêt à utiliser
) else (
    echo ✗ Échec du téléchargement
    echo.
    echo SOLUTION ALTERNATIVE:
    echo Téléchargez manuellement PDFtoPrinter.exe depuis:
    echo https://github.com/jzakiya/PDFtoPrinter/releases
    echo Et placez-le dans le dossier data\
)

echo.
echo ========================================
echo Téléchargement terminé
echo ========================================
pause
