@echo off
echo ========================================
echo BUILD SWAP v1.5.6 - ANTI-GHOSTSCRIPT
echo SOLUTION DÉFINITIVE IMPRESSION NATIVE
echo ========================================
echo.

echo ÉTAPE 1: Vérification de l'environnement...
echo ==========================================
echo.

REM Vérifier Python
python --version
if errorlevel 1 (
    echo ERREUR: Python non trouvé
    pause
    exit /b 1
)

REM Vérifier PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo ✓ Environnement vérifié

echo.
echo ÉTAPE 2: Test de la solution anti-Ghostscript...
echo ===============================================
echo.

echo Test de l'impression native...
python test_impression_anti_ghostscript.py
echo.
echo Appuyez sur une touche pour continuer avec la compilation...
pause

echo.
echo ÉTAPE 3: Nettoyage des anciens builds...
echo =======================================
echo.

REM Nettoyer les anciens builds
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe" del "dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe"

echo ✓ Nettoyage terminé

echo.
echo ÉTAPE 4: Création du fichier spec...
echo ===================================
echo.

REM Créer le fichier spec pour PyInstaller
echo # -*- mode: python ; coding: utf-8 -*- > SWAP_v1.5.6_anti_ghostscript.spec
echo. >> SWAP_v1.5.6_anti_ghostscript.spec
echo block_cipher = None >> SWAP_v1.5.6_anti_ghostscript.spec
echo. >> SWAP_v1.5.6_anti_ghostscript.spec
echo a = Analysis^( >> SWAP_v1.5.6_anti_ghostscript.spec
echo     ['app.py'], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     pathex=[], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     binaries=[], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     datas=[ >> SWAP_v1.5.6_anti_ghostscript.spec
echo         ^('data', 'data'^), >> SWAP_v1.5.6_anti_ghostscript.spec
echo         ^('impression_simple_native.py', '.'^ ), >> SWAP_v1.5.6_anti_ghostscript.spec
echo         ^('solution_impression_native.py', '.'^ ) >> SWAP_v1.5.6_anti_ghostscript.spec
echo     ], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     hiddenimports=[ >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'mysql.connector', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'win32api', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'win32print', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'win32gui', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'win32con', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'reportlab.pdfgen', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'reportlab.lib.pagesizes', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'wkhtmltopdf', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'tkinter', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'tkinter.messagebox', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'tkinter.ttk' >> SWAP_v1.5.6_anti_ghostscript.spec
echo     ], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     hookspath=[], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     hooksconfig={}, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     runtime_hooks=[], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     excludes=[ >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'ghostscript', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'gsprint', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'gswin32', >> SWAP_v1.5.6_anti_ghostscript.spec
echo         'gsdll32' >> SWAP_v1.5.6_anti_ghostscript.spec
echo     ], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     win_no_prefer_redirects=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     win_private_assemblies=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     cipher=block_cipher, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     noarchive=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo ^) >> SWAP_v1.5.6_anti_ghostscript.spec
echo. >> SWAP_v1.5.6_anti_ghostscript.spec
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^) >> SWAP_v1.5.6_anti_ghostscript.spec
echo. >> SWAP_v1.5.6_anti_ghostscript.spec
echo exe = EXE^( >> SWAP_v1.5.6_anti_ghostscript.spec
echo     pyz, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     a.scripts, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     a.binaries, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     a.zipfiles, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     a.datas, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     [], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     name='SWAP_v1.5.6_ANTI_GHOSTSCRIPT', >> SWAP_v1.5.6_anti_ghostscript.spec
echo     debug=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     bootloader_ignore_signals=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     strip=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     upx=True, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     upx_exclude=[], >> SWAP_v1.5.6_anti_ghostscript.spec
echo     console=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     disable_windowed_traceback=False, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     target_arch=None, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     codesign_identity=None, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     entitlements_file=None, >> SWAP_v1.5.6_anti_ghostscript.spec
echo     icon='data/exchange.ico' >> SWAP_v1.5.6_anti_ghostscript.spec
echo ^) >> SWAP_v1.5.6_anti_ghostscript.spec

echo ✓ Fichier spec créé

echo.
echo ÉTAPE 5: Compilation avec PyInstaller...
echo =======================================
echo.

pyinstaller SWAP_v1.5.6_anti_ghostscript.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe" | find ".exe"
    echo.
    
    echo ÉTAPE 6: Création du package de déploiement...
    echo ==============================================
    
    REM Créer le dossier de package
    if not exist "SWAP_v1.5.6_Anti_Ghostscript_Package" mkdir "SWAP_v1.5.6_Anti_Ghostscript_Package"
    
    REM Copier l'exécutable
    copy "dist\SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe" "SWAP_v1.5.6_Anti_Ghostscript_Package\"
    
    REM Copier les fichiers de données
    if exist "data" xcopy "data" "SWAP_v1.5.6_Anti_Ghostscript_Package\data" /E /I /Y
    
    REM Créer le script de lancement
    echo @echo off > "SWAP_v1.5.6_Anti_Ghostscript_Package\run_SWAP_v1.5.6.bat"
    echo echo Lancement de SWAP v1.5.6 Anti-Ghostscript... >> "SWAP_v1.5.6_Anti_Ghostscript_Package\run_SWAP_v1.5.6.bat"
    echo start SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe >> "SWAP_v1.5.6_Anti_Ghostscript_Package\run_SWAP_v1.5.6.bat"
    
    REM Créer le script de vérification
    echo @echo off > "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo VERIFICATION SWAP v1.5.6 ANTI-GHOSTSCRIPT >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo Verification de l'executable... >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo if exist "SWAP_v1.5.6_ANTI_GHOSTSCRIPT.exe" ^( >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo     echo ✓ Executable present >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo ^) else ^( >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo     echo ✗ Executable manquant >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo ^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo Verification du dossier data... >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo if exist "data" ^( >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo     echo ✓ Dossier data present >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo ^) else ^( >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo     echo ✗ Dossier data manquant >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo ^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo SOLUTION ANTI-GHOSTSCRIPT v1.5.6 >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ✓ Adobe Reader ^(priorite 1^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ✓ win32api printto ^(fallback^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ✓ Commande Windows native >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo ✓ Ouverture manuelle ^(dernier recours^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo echo AUCUN GHOSTSCRIPT NE DEVRAIT APPARAITRE ! >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    echo pause >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERIFICATION_v1.5.6.bat"
    
    REM Créer le fichier d'information
    echo SWAP v1.5.6 - SOLUTION ANTI-GHOSTSCRIPT > "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ======================================== >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo PROBLEME RESOLU: Interface Ghostscript >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo SOLUTION: Impression native Windows >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo METHODES D'IMPRESSION: >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo 1. Adobe Reader ^(impression silencieuse^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo 2. win32api printto ^(API Windows^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo 3. Commande Windows print ^(native^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo 4. Ouverture manuelle ^(dernier recours^) >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo. >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo AVANTAGES: >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ✓ Aucune interface Ghostscript >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ✓ Impression automatique maintenue >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ✓ Compatible Windows 64-bit >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ✓ Methodes de fallback multiples >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    echo ✓ Solution native Windows >> "SWAP_v1.5.6_Anti_Ghostscript_Package\VERSION_1.5.6_INFO.txt"
    
    REM Créer l'archive
    echo Création de l'archive...
    powershell Compress-Archive -Path "SWAP_v1.5.6_Anti_Ghostscript_Package\*" -DestinationPath "SWAP_v1.5.6_ANTI_GHOSTSCRIPT.zip" -Force
    
    echo ✓ Package créé: SWAP_v1.5.6_Anti_Ghostscript_Package
    echo ✓ Archive créée: SWAP_v1.5.6_ANTI_GHOSTSCRIPT.zip
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
echo ========================================
echo BUILD TERMINÉ
echo ========================================
echo.
echo RÉSUMÉ:
echo ✓ Solution anti-Ghostscript intégrée
echo ✓ Impression native Windows
echo ✓ Méthodes de fallback multiples
echo ✓ Package de déploiement créé
echo.
echo MÉTHODES D'IMPRESSION:
echo 1. Adobe Reader ^(priorité 1^)
echo 2. win32api printto ^(fallback^)
echo 3. Commande Windows native
echo 4. Ouverture manuelle ^(dernier recours^)
echo.
echo AUCUN GHOSTSCRIPT NE DEVRAIT APPARAÎTRE !
echo.
pause
