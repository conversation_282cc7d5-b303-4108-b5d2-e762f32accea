@echo off
echo ========================================
echo SETUP SWAP v1.5.6 - SOLUTION LOCALE
echo IMPRESSION AUTOMATIQUE SANS TÉLÉCHARGEMENT
echo ========================================
echo.

echo ÉTAPE 1: Création des outils locaux...
echo =====================================
echo.

REM Créer le dossier data s'il n'existe pas
if not exist "data" mkdir "data"

echo [1/3] Création du script PowerShell d'impression...
REM Créer un script PowerShell pour impression directe
echo # Script PowerShell pour impression PDF sans Ghostscript > "data\print-pdf.ps1"
echo param^( >> "data\print-pdf.ps1"
echo     [string]$PdfPath, >> "data\print-pdf.ps1"
echo     [string]$PrinterName >> "data\print-pdf.ps1"
echo ^) >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo Write-Host "Impression PDF: $PdfPath vers $PrinterName" >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo try { >> "data\print-pdf.ps1"
echo     # Méthode 1: Utiliser Adobe Reader si disponible >> "data\print-pdf.ps1"
echo     $adobePaths = @^( >> "data\print-pdf.ps1"
echo         "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe", >> "data\print-pdf.ps1"
echo         "C:\Program Files ^(x86^)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe", >> "data\print-pdf.ps1"
echo         "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" >> "data\print-pdf.ps1"
echo     ^) >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo     $adobeFound = $false >> "data\print-pdf.ps1"
echo     foreach ^($path in $adobePaths^) { >> "data\print-pdf.ps1"
echo         if ^(Test-Path $path^) { >> "data\print-pdf.ps1"
echo             Write-Host "Impression avec Adobe: $path" >> "data\print-pdf.ps1"
echo             $process = Start-Process -FilePath $path -ArgumentList "/t", $PdfPath, $PrinterName -Wait -WindowStyle Hidden -PassThru >> "data\print-pdf.ps1"
echo             if ^($process.ExitCode -eq 0^) { >> "data\print-pdf.ps1"
echo                 Write-Host "Impression Adobe réussie" >> "data\print-pdf.ps1"
echo                 $adobeFound = $true >> "data\print-pdf.ps1"
echo                 break >> "data\print-pdf.ps1"
echo             } >> "data\print-pdf.ps1"
echo         } >> "data\print-pdf.ps1"
echo     } >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo     if ^(-not $adobeFound^) { >> "data\print-pdf.ps1"
echo         # Méthode 2: Utiliser l'association de fichier par défaut >> "data\print-pdf.ps1"
echo         Write-Host "Adobe non trouvé, utilisation association par défaut" >> "data\print-pdf.ps1"
echo         $process = Start-Process -FilePath $PdfPath -Verb "Print" -WindowStyle Hidden -PassThru >> "data\print-pdf.ps1"
echo         Start-Sleep -Seconds 3 >> "data\print-pdf.ps1"
echo         Write-Host "Impression par association de fichier lancée" >> "data\print-pdf.ps1"
echo     } >> "data\print-pdf.ps1"
echo } catch { >> "data\print-pdf.ps1"
echo     Write-Host "Erreur: $_" >> "data\print-pdf.ps1"
echo     exit 1 >> "data\print-pdf.ps1"
echo } >> "data\print-pdf.ps1"

echo ✓ Script PowerShell créé

echo.
echo [2/3] Création du script batch d'impression...
REM Créer un script batch pour impression directe
echo @echo off > "data\print-pdf.bat"
echo REM Script batch pour impression PDF >> "data\print-pdf.bat"
echo set PDF_FILE=%1 >> "data\print-pdf.bat"
echo set PRINTER_NAME=%2 >> "data\print-pdf.bat"
echo. >> "data\print-pdf.bat"
echo echo Impression PDF: %%PDF_FILE%% vers %%PRINTER_NAME%% >> "data\print-pdf.bat"
echo. >> "data\print-pdf.bat"
echo REM Méthode 1: Utiliser la commande print de Windows >> "data\print-pdf.bat"
echo print /D:%%PRINTER_NAME%% "%%PDF_FILE%%" >> "data\print-pdf.bat"
echo if %%ERRORLEVEL%% EQU 0 ( >> "data\print-pdf.bat"
echo     echo Impression Windows réussie >> "data\print-pdf.bat"
echo     exit /b 0 >> "data\print-pdf.bat"
echo ^) >> "data\print-pdf.bat"
echo. >> "data\print-pdf.bat"
echo REM Méthode 2: Copie vers l'imprimante >> "data\print-pdf.bat"
echo copy "%%PDF_FILE%%" "\\localhost\%%PRINTER_NAME%%" >> "data\print-pdf.bat"
echo if %%ERRORLEVEL%% EQU 0 ( >> "data\print-pdf.bat"
echo     echo Impression par copie réussie >> "data\print-pdf.bat"
echo     exit /b 0 >> "data\print-pdf.bat"
echo ^) >> "data\print-pdf.bat"
echo. >> "data\print-pdf.bat"
echo echo Erreur: Impossible d'imprimer >> "data\print-pdf.bat"
echo exit /b 1 >> "data\print-pdf.bat"

echo ✓ Script batch créé

echo.
echo [3/3] Création du script Python d'impression...
REM Créer un script Python pour impression
echo # Script Python pour impression PDF > "data\print_pdf.py"
echo import sys >> "data\print_pdf.py"
echo import os >> "data\print_pdf.py"
echo import subprocess >> "data\print_pdf.py"
echo import win32api >> "data\print_pdf.py"
echo import win32print >> "data\print_pdf.py"
echo. >> "data\print_pdf.py"
echo def print_pdf^(pdf_path, printer_name^): >> "data\print_pdf.py"
echo     """Imprimer un PDF sans déclencher Ghostscript""" >> "data\print_pdf.py"
echo     print^(f"Impression: {pdf_path} vers {printer_name}"^) >> "data\print_pdf.py"
echo. >> "data\print_pdf.py"
echo     # Méthode 1: Utiliser win32api avec printto >> "data\print_pdf.py"
echo     try: >> "data\print_pdf.py"
echo         win32api.ShellExecute^(0, "printto", pdf_path, f'"{printer_name}"', ".", 0^) >> "data\print_pdf.py"
echo         print^("Impression win32api réussie"^) >> "data\print_pdf.py"
echo         return True >> "data\print_pdf.py"
echo     except Exception as e: >> "data\print_pdf.py"
echo         print^(f"Erreur win32api: {e}"^) >> "data\print_pdf.py"
echo. >> "data\print_pdf.py"
echo     # Méthode 2: Ouvrir le PDF ^(sans print^) >> "data\print_pdf.py"
echo     try: >> "data\print_pdf.py"
echo         os.startfile^(pdf_path^) >> "data\print_pdf.py"
echo         print^("PDF ouvert pour impression manuelle"^) >> "data\print_pdf.py"
echo         return True >> "data\print_pdf.py"
echo     except Exception as e: >> "data\print_pdf.py"
echo         print^(f"Erreur ouverture: {e}"^) >> "data\print_pdf.py"
echo         return False >> "data\print_pdf.py"
echo. >> "data\print_pdf.py"
echo if __name__ == "__main__": >> "data\print_pdf.py"
echo     if len^(sys.argv^) != 3: >> "data\print_pdf.py"
echo         print^("Usage: python print_pdf.py fichier.pdf nom_imprimante"^) >> "data\print_pdf.py"
echo         sys.exit^(1^) >> "data\print_pdf.py"
echo. >> "data\print_pdf.py"
echo     pdf_path = sys.argv[1] >> "data\print_pdf.py"
echo     printer_name = sys.argv[2] >> "data\print_pdf.py"
echo     print_pdf^(pdf_path, printer_name^) >> "data\print_pdf.py"

echo ✓ Script Python créé

echo.
echo ÉTAPE 2: Vérification des outils...
echo ===================================
echo.

if exist "data\print-pdf.ps1" (
    echo ✓ Script PowerShell disponible
) else (
    echo ⚠ Script PowerShell manquant
)

if exist "data\print-pdf.bat" (
    echo ✓ Script Batch disponible
) else (
    echo ⚠ Script Batch manquant
)

if exist "data\print_pdf.py" (
    echo ✓ Script Python disponible
) else (
    echo ⚠ Script Python manquant
)

echo.
echo ÉTAPE 3: Compilation de SWAP v1.5.6...
echo ======================================
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT.exe" del "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.6...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT.exe" | find ".exe"
    echo.
    
    echo ÉTAPE 4: Création du package de déploiement...
    echo ==============================================
    call create_package_v1.5.6.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
echo ========================================
echo SETUP TERMINÉ
echo ========================================
echo.
echo RÉSUMÉ:
echo ✓ Scripts d'impression créés localement
echo ✓ SWAP v1.5.6 compilé
echo ✓ Package de déploiement créé
echo.
echo SOLUTION ANTI-GHOSTSCRIPT LOCALE:
echo - Scripts PowerShell/Batch/Python inclus
echo - Pas de téléchargement externe requis
echo - Impression automatique maintenue
echo - Plus d'interface Ghostscript
echo - Compatible Windows 64-bit
echo.
pause
