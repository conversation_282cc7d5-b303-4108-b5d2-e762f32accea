@echo off
echo ========================================
echo SWAP v1.5.1 - Build avec corrections d'impression
echo ========================================
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.1...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe" | find ".exe"
    echo.
    
    REM Créer le package de déploiement
    call create_package_v1.5.1.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
pause
