@echo off
echo ========================================
echo CORRECTION SUMATRAPDF CORROMPU
echo ========================================
echo.

echo Le SumatraPDF actuel semble corrompu.
echo Téléchargement d'une version propre...
echo.

REM Sauvegarder l'ancien SumatraPDF
if exist "SumatraPDF.exe" (
    echo Sauvegarde de l'ancien SumatraPDF...
    move "SumatraPDF.exe" "SumatraPDF_old.exe"
)

REM Télécharger SumatraPDF portable
echo Téléchargement de SumatraPDF portable...
powershell -Command "& {Invoke-WebRequest -Uri 'https://www.sumatrapdfreader.org/dl/rel/3.4.6/SumatraPDF-3.4.6.exe' -OutFile 'SumatraPDF_new.exe'}"

if exist "SumatraPDF_new.exe" (
    echo ✓ Téléchargement réussi
    move "SumatraPDF_new.exe" "SumatraPDF.exe"
    echo ✓ SumatraPDF remplacé
) else (
    echo ❌ Échec du téléchargement
    echo Restauration de l'ancien fichier...
    if exist "SumatraPDF_old.exe" (
        move "SumatraPDF_old.exe" "SumatraPDF.exe"
    )
)

echo.
echo ========================================
echo ALTERNATIVE: IMPRESSION SANS SUMATRAPDF
echo ========================================
echo.
echo La nouvelle version de SWAP (v1.5.3) n'utilise plus SumatraPDF
echo Elle utilise les méthodes d'impression Windows natives
echo Cela évite les problèmes de corruption et d'interface Ghostscript
echo.

pause
