@echo off
echo ========================================
echo SWAP v1.5.2 - Build SANS interface Ghostscript
echo ========================================
echo.
echo CORRECTIONS APPORTÉES:
echo ✓ Plus d'interface Ghostscript qui s'ouvre
echo ✓ Impression silencieuse avec SumatraPDF
echo ✓ Fallback vers impression Windows
echo ✓ Logique d'impression intelligente maintenue
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.2_IMPRESSION_SANS_GHOSTSCRIPT.exe" del "dist\SWAP_v1.5.2_IMPRESSION_SANS_GHOSTSCRIPT.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.2...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.2_IMPRESSION_SANS_GHOSTSCRIPT.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.2_IMPRESSION_SANS_GHOSTSCRIPT.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.2_IMPRESSION_SANS_GHOSTSCRIPT.exe" | find ".exe"
    echo.
    
    REM Créer le package de déploiement
    call create_package_v1.5.2.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
pause
