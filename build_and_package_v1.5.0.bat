@echo off
echo ========================================
echo SWAP Version 1.5.0 - Build Complet
echo Correction d'impression intelligente
echo ========================================

echo.
echo Etape 1: Build de l'executable...
call build_v1.5.0.bat

if errorlevel 1 (
    echo ERREUR lors du build de l'executable
    pause
    exit /b 1
)

echo.
echo Etape 2: Creation du package...
call create_package_v1.5.0.bat

echo.
echo ========================================
echo BUILD COMPLET TERMINE!
echo ========================================
echo.
echo Fichiers crees:
echo - dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe
echo - SWAP_v1.5.0_Package\ (dossier)
echo - SWAP_v1.5.0_Package.zip (archive)
echo.
echo Le package est pret pour le deploiement!
echo.
pause
