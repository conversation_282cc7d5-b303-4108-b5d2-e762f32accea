@echo off
echo ========================================
echo Création du package SWAP v1.5.5
echo ========================================
echo.

REM Créer le dossier de package
set PACKAGE_DIR=SWAP_v1.5.5_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

REM Copier l'exécutable principal
echo Copie de l'exécutable principal...
copy "dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe" "%PACKAGE_DIR%\"

REM Copier les fichiers de données nécessaires
echo Copie des fichiers de données...
xcopy "data" "%PACKAGE_DIR%\data" /E /I /Y
copy ".env" "%PACKAGE_DIR%\" 2>nul

REM Créer le script de lancement
echo Création du script de lancement...
echo @echo off > "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo SWAP v1.5.5 - Impression Automatique Efficace >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo SOLUTION GHOSTSCRIPT: >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo - Impression automatique maintenue >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo - Utilise Adobe Reader ou win32api printto >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo - Évite complètement Ghostscript >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo - Impression directe et efficace >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo echo Démarrage de l'application... >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo if errorlevel 1 ( >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo     echo ERREUR: Impossible de démarrer l'application >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo     pause >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"
echo ^) >> "%PACKAGE_DIR%\run_SWAP_v1.5.5.bat"

REM Créer le script de vérification
echo Création du script de vérification...
echo @echo off > "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo SWAP v1.5.5 - Vérification du système >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo Vérification des imprimantes disponibles... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo Vérification Adobe Reader... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo if exist "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe" echo ✓ Adobe Acrobat DC trouvé >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo if exist "C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" echo ✓ Adobe Reader DC (x86) trouvé >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo if exist "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" echo ✓ Adobe Reader DC trouvé >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo echo Vérification terminée. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"
echo pause >> "%PACKAGE_DIR%\VERIFICATION_v1.5.5.bat"

REM Créer le fichier d'information
echo Création du fichier d'information...
echo SWAP Version 1.5.5 - Impression Automatique Efficace > "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ======================================== >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo Date de création: %date% %time% >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo PROBLÈME GHOSTSCRIPT RÉSOLU: >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ============================ >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Plus d'interface Ghostscript qui s'ouvre >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Impression automatique MAINTENUE >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Utilise Adobe Reader (si disponible) >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Fallback vers win32api printto >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Impression directe et efficace >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo MÉTHODES D'IMPRESSION: >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ===================== >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 1. Adobe Reader /t (impression silencieuse) - Priorité 1 >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 2. win32api.ShellExecute printto - Fallback >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 3. Évite os.startfile print (qui déclenche Ghostscript) >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo UTILISATION: >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ============ >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 1. Scanner un SN >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 2. L'impression se fait automatiquement >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 3. Plus d'interface Ghostscript >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 4. Impression silencieuse et efficace >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo CORRECTIONS MAINTENUES: >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ======================= >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Logique intelligente de sélection d'imprimante >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Priorité 1: HP LaserJet M109-M112 >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Priorité 2: EPSON ET contenant 2810 >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ Priorité 3: Imprimante par défaut (si non-POSTEK) >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ✓ POSTEK réservé uniquement aux étiquettes outbound >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo ============= >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 1. Copier tout le dossier SWAP_v1.5.5_Package sur le PC cible >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 2. Exécuter VERIFICATION_v1.5.5.bat pour vérifier le système >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.5.5.bat >> "%PACKAGE_DIR%\VERSION_1.5.5_INFO.txt"

REM Créer l'archive ZIP
echo Création de l'archive ZIP...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath 'SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE.zip' -Force"

echo.
echo ========================================
echo ✓ PACKAGE CRÉÉ AVEC SUCCÈS !
echo ========================================
echo.
echo Dossier: %PACKAGE_DIR%
echo Archive: SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE.zip
echo.
echo SOLUTION FINALE:
echo - Impression automatique maintenue
echo - Plus d'interface Ghostscript
echo - Utilise Adobe Reader ou win32api printto
echo - Impression directe et efficace
echo.
echo Le package est prêt pour le déploiement !
echo.
