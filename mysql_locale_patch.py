
# PATCH MYSQL LOCALE - À ajouter au début de app.py

import os
import locale

# FIX MYSQL LOCALE ERROR
def fix_mysql_locale():
    """Corriger l'erreur de locale MySQL"""
    try:
        # Méthode 1: Locale C (universelle)
        locale.setlocale(locale.LC_ALL, 'C')
        os.environ['LC_ALL'] = 'C'
        os.environ['LANG'] = 'C'
        os.environ['LANGUAGE'] = 'en'
        return True
    except:
        try:
            # Méthode 2: Locale anglaise
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            os.environ['LC_ALL'] = 'en_US.UTF-8'
            os.environ['LANG'] = 'en_US.UTF-8'
            return True
        except:
            return False

# Appliquer le fix avant tout import MySQL
fix_mysql_locale()

# Maintenant importer mysql.connector
import mysql.connector

# Reste du code app.py...
