# Script PowerShell simple pour créer l'exécutable SWAP
Write-Host "===============================================" -ForegroundColor Green
Write-Host "     CREATION DE L'EXECUTABLE SWAP v1.4.3" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Vérifier si Python est installé
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERREUR: Python n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    exit 1
}

# Installer PyInstaller si nécessaire
Write-Host "Vérification de PyInstaller..." -ForegroundColor Yellow
try {
    pip show pyinstaller | Out-Null
    Write-Host "PyInstaller déjà installé" -ForegroundColor Green
} catch {
    Write-Host "Installation de PyInstaller..." -ForegroundColor Yellow
    pip install pyinstaller
}

# Nettoyer les anciens builds
Write-Host "Nettoyage des anciens builds..." -ForegroundColor Yellow
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
Get-ChildItem -Filter "*.spec" | Remove-Item -Force

Write-Host ""
Write-Host "Construction de l'exécutable..." -ForegroundColor Cyan
Write-Host "Cela peut prendre plusieurs minutes..." -ForegroundColor Yellow

# Commande PyInstaller simplifiée
$command = "pyinstaller --onefile --windowed --name=SWAP_v1.4.3 --add-data=`".env;.`" --add-data=`"data;data`" --icon=`"data\exchange.ico`" app.py"

try {
    Invoke-Expression $command
    if ($LASTEXITCODE -ne 0) {
        throw "PyInstaller a échoué"
    }
} catch {
    Write-Host ""
    Write-Host "ERREUR: La construction de l'exécutable a échoué." -ForegroundColor Red
    Write-Host "Essayons sans icône..." -ForegroundColor Yellow
    
    # Essayer sans icône
    $commandNoIcon = "pyinstaller --onefile --windowed --name=SWAP_v1.4.3 --add-data=`".env;.`" --add-data=`"data;data`" app.py"
    try {
        Invoke-Expression $commandNoIcon
        if ($LASTEXITCODE -ne 0) {
            throw "PyInstaller a échoué même sans icône"
        }
    } catch {
        Write-Host "ERREUR: Impossible de créer l'exécutable" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 1
    }
}

# Vérifier si l'exécutable a été créé
if (Test-Path "dist\SWAP_v1.4.3.exe") {
    Write-Host ""
    Write-Host "✓ Exécutable créé avec succès!" -ForegroundColor Green
    
    # Créer le dossier de distribution
    Write-Host "Création du package de distribution..." -ForegroundColor Yellow
    if (-not (Test-Path "SWAP_Package")) { New-Item -ItemType Directory -Name "SWAP_Package" }
    
    # Copier l'exécutable
    Copy-Item "dist\SWAP_v1.4.3.exe" "SWAP_Package\"
    
    # Copier les fichiers nécessaires
    if (Test-Path ".env") { Copy-Item ".env" "SWAP_Package\" }
    if (Test-Path "data") { Copy-Item -Recurse "data" "SWAP_Package\" -Force }
    if (Test-Path "SumatraPDF.exe") { Copy-Item "SumatraPDF.exe" "SWAP_Package\" }
    
    # Créer le fichier README
    $readmeContent = @"
SWAP Application v1.4.3
========================

INSTALLATION:
1. Copiez tout le contenu de ce dossier sur l'autre PC
2. Lancez SWAP_v1.4.3.exe

CONFIGURATION:
- Modifiez le fichier .env pour configurer la base de donnees
- Assurez-vous que les imprimantes sont installees

IMPRIMANTES SUPPORTEES:
- HP LaserJet M109-M112 (priorite 1)
- EPSON ET 2810 (priorite 2)
- POSTEK (pour etiquettes outbound)
- Imprimante par defaut (si pas POSTEK)

CONFIGURATION BASE DE DONNEES (.env):
DB_HOST_IP=votre_serveur_mysql
DB_USER=votre_utilisateur
DB_PASSWORD=votre_mot_de_passe
DB_NAME=votre_base_de_donnees
DB_PORT=3306
"@
    
    $readmeContent | Out-File -FilePath "SWAP_Package\README.txt" -Encoding UTF8
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "     CONSTRUCTION TERMINÉE AVEC SUCCÈS !" -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Fichiers créés dans SWAP_Package:" -ForegroundColor Cyan
    Get-ChildItem "SWAP_Package" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
    Write-Host ""
    Write-Host "Vous pouvez maintenant copier le dossier SWAP_Package sur l'autre PC." -ForegroundColor Yellow
    
} else {
    Write-Host ""
    Write-Host "ERREUR: L'exécutable n'a pas été créé." -ForegroundColor Red
}

Write-Host ""
Read-Host "Appuyez sur Entree pour terminer"
