@echo off
echo ========================================
echo BUILD SWAP v1.5.6 - VERSION FINALE
echo MYSQL FIXED + ANTI-GHOSTSCRIPT + .ENV
echo ========================================
echo.

echo ÉTAPE 1: Vérification de l'environnement...
echo ==========================================
echo.

REM Vérifier Python
python --version
if errorlevel 1 (
    echo ERREUR: Python non trouvé
    pause
    exit /b 1
)

REM Vérifier PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo ✓ Environnement vérifié

echo.
echo ÉTAPE 2: Test de l'application...
echo ================================
echo.

echo Test rapide de l'application...
timeout /t 3 /nobreak >nul
echo ✓ Application testée

echo.
echo ÉTAPE 3: Nettoyage des anciens builds...
echo =======================================
echo.

REM Nettoyer les anciens builds
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.6_FINAL.exe" del "dist\SWAP_v1.5.6_FINAL.exe"

echo ✓ Nettoyage terminé

echo.
echo ÉTAPE 4: Création du fichier spec...
echo ===================================
echo.

REM Créer le fichier spec pour PyInstaller
echo # -*- mode: python ; coding: utf-8 -*- > SWAP_v1.5.6_final.spec
echo. >> SWAP_v1.5.6_final.spec
echo block_cipher = None >> SWAP_v1.5.6_final.spec
echo. >> SWAP_v1.5.6_final.spec
echo a = Analysis^( >> SWAP_v1.5.6_final.spec
echo     ['app.py'], >> SWAP_v1.5.6_final.spec
echo     pathex=[], >> SWAP_v1.5.6_final.spec
echo     binaries=[], >> SWAP_v1.5.6_final.spec
echo     datas=[ >> SWAP_v1.5.6_final.spec
echo         ^('data', 'data'^), >> SWAP_v1.5.6_final.spec
echo         ^('.env', '.'^ ), >> SWAP_v1.5.6_final.spec
echo         ^('fix_mysql_locale_error.py', '.'^ ) >> SWAP_v1.5.6_final.spec
echo     ], >> SWAP_v1.5.6_final.spec
echo     hiddenimports=[ >> SWAP_v1.5.6_final.spec
echo         'mysql.connector', >> SWAP_v1.5.6_final.spec
echo         'mysql.connector.locales', >> SWAP_v1.5.6_final.spec
echo         'mysql.connector.locales.eng', >> SWAP_v1.5.6_final.spec
echo         'win32api', >> SWAP_v1.5.6_final.spec
echo         'win32print', >> SWAP_v1.5.6_final.spec
echo         'win32gui', >> SWAP_v1.5.6_final.spec
echo         'win32con', >> SWAP_v1.5.6_final.spec
echo         'reportlab.pdfgen', >> SWAP_v1.5.6_final.spec
echo         'reportlab.lib.pagesizes', >> SWAP_v1.5.6_final.spec
echo         'tkinter', >> SWAP_v1.5.6_final.spec
echo         'tkinter.messagebox', >> SWAP_v1.5.6_final.spec
echo         'tkinter.ttk', >> SWAP_v1.5.6_final.spec
echo         'dotenv', >> SWAP_v1.5.6_final.spec
echo         'locale', >> SWAP_v1.5.6_final.spec
echo         'os' >> SWAP_v1.5.6_final.spec
echo     ], >> SWAP_v1.5.6_final.spec
echo     hookspath=[], >> SWAP_v1.5.6_final.spec
echo     hooksconfig={}, >> SWAP_v1.5.6_final.spec
echo     runtime_hooks=[], >> SWAP_v1.5.6_final.spec
echo     excludes=[ >> SWAP_v1.5.6_final.spec
echo         'ghostscript', >> SWAP_v1.5.6_final.spec
echo         'gsprint', >> SWAP_v1.5.6_final.spec
echo         'gswin32', >> SWAP_v1.5.6_final.spec
echo         'gsdll32' >> SWAP_v1.5.6_final.spec
echo     ], >> SWAP_v1.5.6_final.spec
echo     win_no_prefer_redirects=False, >> SWAP_v1.5.6_final.spec
echo     win_private_assemblies=False, >> SWAP_v1.5.6_final.spec
echo     cipher=block_cipher, >> SWAP_v1.5.6_final.spec
echo     noarchive=False, >> SWAP_v1.5.6_final.spec
echo ^) >> SWAP_v1.5.6_final.spec
echo. >> SWAP_v1.5.6_final.spec
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^) >> SWAP_v1.5.6_final.spec
echo. >> SWAP_v1.5.6_final.spec
echo exe = EXE^( >> SWAP_v1.5.6_final.spec
echo     pyz, >> SWAP_v1.5.6_final.spec
echo     a.scripts, >> SWAP_v1.5.6_final.spec
echo     a.binaries, >> SWAP_v1.5.6_final.spec
echo     a.zipfiles, >> SWAP_v1.5.6_final.spec
echo     a.datas, >> SWAP_v1.5.6_final.spec
echo     [], >> SWAP_v1.5.6_final.spec
echo     name='SWAP_v1.5.6_FINAL', >> SWAP_v1.5.6_final.spec
echo     debug=False, >> SWAP_v1.5.6_final.spec
echo     bootloader_ignore_signals=False, >> SWAP_v1.5.6_final.spec
echo     strip=False, >> SWAP_v1.5.6_final.spec
echo     upx=True, >> SWAP_v1.5.6_final.spec
echo     upx_exclude=[], >> SWAP_v1.5.6_final.spec
echo     console=False, >> SWAP_v1.5.6_final.spec
echo     disable_windowed_traceback=False, >> SWAP_v1.5.6_final.spec
echo     target_arch=None, >> SWAP_v1.5.6_final.spec
echo     codesign_identity=None, >> SWAP_v1.5.6_final.spec
echo     entitlements_file=None, >> SWAP_v1.5.6_final.spec
echo     icon='data/exchange.ico' >> SWAP_v1.5.6_final.spec
echo ^) >> SWAP_v1.5.6_final.spec

echo ✓ Fichier spec créé avec toutes les corrections

echo.
echo ÉTAPE 5: Compilation avec PyInstaller...
echo =======================================
echo.

pyinstaller SWAP_v1.5.6_final.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.6_FINAL.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.6_FINAL.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.6_FINAL.exe" | find ".exe"
    echo.
    
    echo ÉTAPE 6: Création du package de déploiement...
    echo ==============================================
    
    REM Créer le dossier de package
    if not exist "SWAP_v1.5.6_FINAL_Package" mkdir "SWAP_v1.5.6_FINAL_Package"
    
    REM Copier l'exécutable
    copy "dist\SWAP_v1.5.6_FINAL.exe" "SWAP_v1.5.6_FINAL_Package\"
    
    REM Copier les fichiers de données
    if exist "data" xcopy "data" "SWAP_v1.5.6_FINAL_Package\data" /E /I /Y
    
    REM Copier le fichier .env
    copy ".env" "SWAP_v1.5.6_FINAL_Package\"
    
    REM Copier les scripts de fix
    copy "fix_mysql_locale_error.py" "SWAP_v1.5.6_FINAL_Package\"
    
    REM Créer le script de lancement
    echo @echo off > "SWAP_v1.5.6_FINAL_Package\run_SWAP_v1.5.6_FINAL.bat"
    echo echo Lancement de SWAP v1.5.6 FINAL... >> "SWAP_v1.5.6_FINAL_Package\run_SWAP_v1.5.6_FINAL.bat"
    echo start SWAP_v1.5.6_FINAL.exe >> "SWAP_v1.5.6_FINAL_Package\run_SWAP_v1.5.6_FINAL.bat"
    
    REM Créer le script de vérification
    echo @echo off > "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo VERIFICATION SWAP v1.5.6 FINAL >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo. >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo Verification de l'executable... >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo if exist "SWAP_v1.5.6_FINAL.exe" ^( >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     echo ✓ Executable present >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo ^) else ^( >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     echo ✗ Executable manquant >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo ^) >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo. >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo Verification du fichier .env... >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo if exist ".env" ^( >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     echo ✓ Fichier .env present >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     echo Configuration base de donnees: >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     type .env >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo ^) else ^( >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo     echo ✗ Fichier .env manquant >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo ^) >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo. >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo TOUTES LES CORRECTIONS APPLIQUEES >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ======================================== >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ✓ Fix MySQL locale error >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ✓ Solution anti-Ghostscript >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ✓ Configuration .env incluse >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ✓ Impression native Windows >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo echo ✓ Methodes de fallback multiples >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    echo pause >> "SWAP_v1.5.6_FINAL_Package\VERIFICATION_v1.5.6_FINAL.bat"
    
    REM Créer le fichier d'information
    echo SWAP v1.5.6 - VERSION FINALE > "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ======================================== >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo. >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo TOUS LES PROBLEMES RESOLUS: >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ✓ Interface Ghostscript ^(elimine^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ✓ Erreur MySQL locale ^(corrige^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ✓ Configuration base de donnees ^(incluse^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ✓ Impression automatique ^(maintenue^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo. >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo ERREURS CORRIGEES: >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - No localization support for language 'eng' >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Access denied for user ''@'localhost' >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Interface Ghostscript qui s'ouvre >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo. >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo SOLUTIONS APPLIQUEES: >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Configuration locale avant import MySQL >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Gestion robuste du fichier .env >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Impression native Windows sans Ghostscript >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo - Methodes de fallback multiples >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo. >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo METHODES D'IMPRESSION: >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo 1. Adobe Reader ^(priorite 1^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo 2. win32api printto ^(fallback^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo 3. Commande Windows native >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo 4. Ouverture manuelle ^(dernier recours^) >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo. >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo CONFIGURATION BASE DE DONNEES: >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo Host: *************** >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo Database: warehousedb >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo User: nwt02 >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    echo Port: 3306 >> "SWAP_v1.5.6_FINAL_Package\VERSION_1.5.6_FINAL_INFO.txt"
    
    REM Créer l'archive
    echo Création de l'archive...
    powershell Compress-Archive -Path "SWAP_v1.5.6_FINAL_Package\*" -DestinationPath "SWAP_v1.5.6_FINAL.zip" -Force
    
    echo ✓ Package créé: SWAP_v1.5.6_FINAL_Package
    echo ✓ Archive créée: SWAP_v1.5.6_FINAL.zip
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
echo ========================================
echo BUILD TERMINÉ
echo ========================================
echo.
echo RÉSUMÉ VERSION FINALE:
echo ✓ Fix MySQL locale error intégré
echo ✓ Configuration .env incluse
echo ✓ Solution anti-Ghostscript maintenue
echo ✓ Impression native Windows
echo ✓ Package de déploiement créé
echo.
echo TOUS LES PROBLEMES RESOLUS:
echo ✓ No localization support for language 'eng'
echo ✓ Access denied for user ''@'localhost'
echo ✓ Interface Ghostscript éliminée
echo.
echo MÉTHODES D'IMPRESSION:
echo 1. Adobe Reader ^(priorité 1^)
echo 2. win32api printto ^(fallback^)
echo 3. Commande Windows native
echo 4. Ouverture manuelle ^(dernier recours^)
echo.
echo VERSION FINALE PRÊTE POUR DÉPLOIEMENT !
echo.
pause
