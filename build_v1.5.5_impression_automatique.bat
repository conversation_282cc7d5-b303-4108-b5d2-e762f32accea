@echo off
echo ========================================
echo SWAP v1.5.5 - Build IMPRESSION AUTOMATIQUE EFFICACE
echo ========================================
echo.
echo SOLUTION FINALE AU PROBLÈME GHOSTSCRIPT:
echo ✓ Impression automatique MAINTENUE
echo ✓ Utilise Adobe Reader ou win32api printto
echo ✓ Évite complètement Ghostscript
echo ✓ Impression directe et efficace
echo ✓ Logique d'impression intelligente maintenue
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe" del "dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.5...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.5_IMPRESSION_AUTOMATIQUE_EFFICACE.exe" | find ".exe"
    echo.
    
    REM Créer le package de déploiement
    call create_package_v1.5.5.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
pause
