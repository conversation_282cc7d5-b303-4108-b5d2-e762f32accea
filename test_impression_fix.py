#!/usr/bin/env python3
"""
Test de la correction d'impression pour SWAP v1.5.1
"""

import win32print
import os
import subprocess

def test_impression_logic():
    """Test de la logique d'impression corrigée"""
    
    print("=== TEST DE LA LOGIQUE D'IMPRESSION CORRIGÉE ===")
    print()
    
    # Obtenir l'imprimante par défaut
    try:
        default_printer = win32print.GetDefaultPrinter()
        print(f"Imprimante par défaut: {default_printer}")
    except Exception as e:
        print(f"Erreur lors de l'obtention de l'imprimante par défaut: {e}")
        default_printer = None
    
    # Obtenir la liste des imprimantes disponibles
    try:
        available_printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
        print(f"Imprimantes disponibles: {available_printers}")
    except Exception as e:
        print(f"Erreur lors de l'énumération des imprimantes: {e}")
        available_printers = []
    
    print()
    
    # Simuler la logique de sélection d'imprimante (comme dans app.py)
    printer_name = None
    
    # 1. Chercher HP LaserJet M109-M112
    for printer in available_printers:
        if "HP LaserJet M109-M112" in printer:
            printer_name = printer
            print(f'✓ HP LaserJet M109-M112 sélectionnée: {printer_name}')
            break
    
    # 2. Si pas trouvée, chercher EPSON avec 2810
    if printer_name is None:
        for printer in available_printers:
            if "EPSON" in printer and "2810" in printer:
                printer_name = printer
                print(f'✓ EPSON 2810 sélectionnée: {printer_name}')
                break
    
    # 3. Si aucune des deux n'est trouvée, utiliser l'imprimante par défaut (sauf si c'est POSTEK)
    if printer_name is None:
        if default_printer and "POSTEK" not in default_printer:
            printer_name = default_printer
            print(f'✓ Imprimante par défaut sélectionnée: {printer_name}')
        else:
            # Si l'imprimante par défaut est POSTEK, chercher une autre imprimante
            for printer in available_printers:
                if "POSTEK" not in printer:
                    printer_name = printer
                    print(f'✓ Alternative à POSTEK sélectionnée: {printer_name}')
                    break
    
    # Si aucune imprimante appropriée n'est trouvée, utiliser la première non-POSTEK
    if printer_name is None:
        for printer in available_printers:
            if "POSTEK" not in printer:
                printer_name = printer
                print(f'✓ Première imprimante non-POSTEK sélectionnée: {printer_name}')
                break
    
    if printer_name is None:
        print("❌ Aucune imprimante appropriée trouvée!")
        return False
    
    print()
    print(f"🎯 IMPRIMANTE FINALE SÉLECTIONNÉE: {printer_name}")
    print()
    
    # Test de la méthode d'impression
    print("=== TEST DE LA MÉTHODE D'IMPRESSION ===")
    
    # Vérifier SumatraPDF
    sumatra_paths = [
        r'D:\SWAP\SumatraPDF.exe',
        r'D:\SWAP\SWAP_v1.5.1_Package\SumatraPDF.exe',
        'SumatraPDF.exe'
    ]
    
    sumatra_found = False
    for path in sumatra_paths:
        if os.path.exists(path):
            print(f"✓ SumatraPDF trouvé: {path}")
            sumatra_found = True
            break
    
    if not sumatra_found:
        print("⚠ SumatraPDF non trouvé - utilisation impression Windows par défaut")
    
    print()
    
    # Vérifier les imprimantes POSTEK pour outbound
    postek_printers = [printer for printer in available_printers if "POSTEK" in printer]
    if postek_printers:
        print(f"📄 Imprimantes POSTEK pour étiquettes outbound: {postek_printers}")
    else:
        print("📄 Aucune imprimante POSTEK trouvée pour étiquettes outbound")
    
    print()
    print("=== RÉSUMÉ DES CORRECTIONS ===")
    print("✅ Logique de sélection d'imprimante intelligente")
    print("✅ HP LaserJet M109-M112 prioritaire")
    print("✅ EPSON ET-2810 en alternative")
    print("✅ POSTEK exclu des PDF principaux")
    print("✅ POSTEK réservé aux étiquettes outbound")
    print("✅ Impression simplifiée (plus d'interface Ghostscript)")
    print("✅ Méthode SumatraPDF silencieuse")
    print("✅ Fallback vers impression Windows")
    
    return True

if __name__ == "__main__":
    test_impression_logic()
