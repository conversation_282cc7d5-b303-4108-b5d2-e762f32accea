#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test d'impression PDF 64-bit pour SWAP v1.5.6
Vérifie que les outils d'impression fonctionnent sans Ghostscript
"""

import os
import sys
import subprocess
import tempfile
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import win32print

def create_test_pdf():
    """Créer un PDF de test simple"""
    temp_dir = tempfile.gettempdir()
    pdf_path = os.path.join(temp_dir, "test_impression_swap.pdf")
    
    # Créer un PDF simple avec ReportLab
    c = canvas.Canvas(pdf_path, pagesize=letter)
    c.drawString(100, 750, "TEST IMPRESSION SWAP v1.5.6")
    c.drawString(100, 720, "Solution 64-bit Anti-Ghostscript")
    c.drawString(100, 690, "=" * 50)
    c.drawString(100, 660, "✓ SumatraPDF 64-bit")
    c.drawString(100, 630, "✓ Script PowerShell")
    c.drawString(100, 600, "✓ Impression Windows native")
    c.drawString(100, 570, "✓ Compatible Windows 64-bit")
    c.drawString(100, 540, "=" * 50)
    c.drawString(100, 510, "Si vous voyez cette page imprimée,")
    c.drawString(100, 480, "la solution fonctionne parfaitement !")
    c.save()
    
    print(f"✓ PDF de test créé: {pdf_path}")
    return pdf_path

def get_default_printer():
    """Obtenir l'imprimante par défaut"""
    try:
        default_printer = win32print.GetDefaultPrinter()
        print(f"✓ Imprimante par défaut: {default_printer}")
        return default_printer
    except Exception as e:
        print(f"⚠ Erreur obtention imprimante par défaut: {e}")
        return None

def test_sumatra_pdf(pdf_path, printer_name):
    """Tester l'impression avec SumatraPDF"""
    print("\n=== TEST SUMATRAPDF 64-BIT ===")
    
    # Chemins possibles pour SumatraPDF
    sumatra_paths = [
        os.path.join("data", "SumatraPDF.exe"),
        r"C:\Program Files\SumatraPDF\SumatraPDF.exe",
        r"C:\Program Files (x86)\SumatraPDF\SumatraPDF.exe"
    ]
    
    for sumatra_path in sumatra_paths:
        if os.path.exists(sumatra_path):
            print(f"✓ SumatraPDF trouvé: {sumatra_path}")
            try:
                print(f"Impression vers: {printer_name}")
                result = subprocess.run([
                    sumatra_path,
                    '-print-to', printer_name,
                    '-silent',
                    pdf_path
                ], check=False, timeout=30, capture_output=True, text=True,
                  creationflags=subprocess.CREATE_NO_WINDOW)
                
                if result.returncode == 0:
                    print("✅ IMPRESSION SUMATRAPDF RÉUSSIE")
                    return True
                else:
                    print(f"❌ Erreur SumatraPDF: {result.stderr}")
                    return False
            except Exception as e:
                print(f"❌ Exception SumatraPDF: {e}")
                return False
    
    print("⚠ SumatraPDF non trouvé")
    return False

def test_powershell_script(pdf_path, printer_name):
    """Tester l'impression avec le script PowerShell"""
    print("\n=== TEST SCRIPT POWERSHELL ===")
    
    script_path = os.path.join("data", "print-pdf.ps1")
    if os.path.exists(script_path):
        print(f"✓ Script PowerShell trouvé: {script_path}")
        try:
            print(f"Impression vers: {printer_name}")
            result = subprocess.run([
                'powershell.exe',
                '-ExecutionPolicy', 'Bypass',
                '-File', script_path,
                '-PdfPath', pdf_path,
                '-PrinterName', printer_name
            ], check=False, timeout=30, capture_output=True, text=True,
              creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode == 0:
                print("✅ IMPRESSION POWERSHELL RÉUSSIE")
                return True
            else:
                print(f"❌ Erreur PowerShell: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Exception PowerShell: {e}")
            return False
    else:
        print("⚠ Script PowerShell non trouvé")
        return False

def test_windows_native(pdf_path, printer_name):
    """Tester l'impression Windows native"""
    print("\n=== TEST IMPRESSION WINDOWS NATIVE ===")
    
    try:
        print(f"Impression vers: {printer_name}")
        result = subprocess.run([
            'cmd.exe', '/c', 'print',
            f'/D:{printer_name}',
            pdf_path
        ], check=False, timeout=30, capture_output=True, text=True,
          creationflags=subprocess.CREATE_NO_WINDOW)
        
        if result.returncode == 0:
            print("✅ IMPRESSION WINDOWS NATIVE RÉUSSIE")
            return True
        else:
            print(f"❌ Erreur impression native: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Exception impression native: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=" * 60)
    print("TEST IMPRESSION SWAP v1.5.6 - SOLUTION 64-BIT")
    print("=" * 60)
    
    # Créer un PDF de test
    pdf_path = create_test_pdf()
    
    # Obtenir l'imprimante par défaut
    printer_name = get_default_printer()
    if not printer_name:
        print("❌ Impossible de continuer sans imprimante")
        return
    
    # Tests des différentes méthodes
    success_count = 0
    total_tests = 3
    
    # Test 1: SumatraPDF
    if test_sumatra_pdf(pdf_path, printer_name):
        success_count += 1
    
    # Test 2: Script PowerShell
    if test_powershell_script(pdf_path, printer_name):
        success_count += 1
    
    # Test 3: Impression Windows native
    if test_windows_native(pdf_path, printer_name):
        success_count += 1
    
    # Résultats
    print("\n" + "=" * 60)
    print("RÉSULTATS DES TESTS")
    print("=" * 60)
    print(f"Tests réussis: {success_count}/{total_tests}")
    
    if success_count > 0:
        print("✅ AU MOINS UNE MÉTHODE FONCTIONNE")
        print("✅ SOLUTION ANTI-GHOSTSCRIPT OPÉRATIONNELLE")
        print("✅ Vérifiez votre imprimante pour confirmer l'impression")
    else:
        print("❌ AUCUNE MÉTHODE N'A FONCTIONNÉ")
        print("❌ Vérifiez la configuration du système")
    
    # Nettoyage
    try:
        os.remove(pdf_path)
        print(f"✓ PDF de test supprimé: {pdf_path}")
    except:
        pass
    
    print("\n" + "=" * 60)
    print("TEST TERMINÉ")
    print("=" * 60)

if __name__ == "__main__":
    main()
