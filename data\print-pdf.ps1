# Script PowerShell pour impression PDF 
param( 
    [string]$PdfPath, 
    [string]$PrinterName 
) 
 
try { 
    # Méthode 1: Utiliser Adobe Reader si disponible 
    $adobePaths = @( 
        "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe", 
        "C:\Program Files ^(x86^)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe", 
        "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" 
    ) 
 
    $adobeFound = $false 
    foreach ($path in $adobePaths) { 
        if (Test-Path $path) { 
            Write-Host "Impression avec Adobe: $path" 
            Start-Process -FilePath $path -ArgumentList "/t", $PdfPath, $PrinterName -Wait -WindowStyle Hidden 
            $adobeFound = $true 
            break 
        } 
    } 
 
    if (-not $adobeFound) { 
        # Méthode 2: Utiliser l'impression Windows native 
        Write-Host "Adobe non trouvé, utilisation impression Windows" 
        $printJob = Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "copy", "`"$PdfPath`"", "`"\\localhost\$PrinterName`"" -Wait -WindowStyle Hidden -PassThru 
        if ($printJob.ExitCode -eq 0) { 
            Write-Host "Impression réussie" 
        } else { 
            Write-Host "Erreur impression" 
        } 
    } 
} catch { 
    Write-Host "Erreur: $_" 
} 
