@echo off
echo ========================================
echo Création du package SWAP v1.5.6
echo ========================================
echo.

REM Créer le dossier de package
set PACKAGE_DIR=SWAP_v1.5.6_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

REM Copier l'exécutable principal
echo Copie de l'exécutable principal...
copy "dist\SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe" "%PACKAGE_DIR%\"

REM Copier les fichiers de données nécessaires
echo Copie des fichiers de données...
xcopy "data" "%PACKAGE_DIR%\data" /E /I /Y
copy ".env" "%PACKAGE_DIR%\" 2>nul

REM Créer le script de lancement
echo Création du script de lancement...
echo @echo off > "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo SWAP v1.5.6 - Impression Automatique avec Outils >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo SOLUTION ANTI-GHOSTSCRIPT: >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo - Impression automatique avec PDFtoPrinter >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo - Fallback avec SumatraPDF >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo - Évite complètement Ghostscript >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo - Impression directe et fiable >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo echo Démarrage de l'application... >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.exe >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo if errorlevel 1 ( >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo     echo ERREUR: Impossible de démarrer l'application >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo     pause >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"
echo ^) >> "%PACKAGE_DIR%\run_SWAP_v1.5.6.bat"

REM Créer le script de vérification
echo Création du script de vérification...
echo @echo off > "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo SWAP v1.5.6 - Vérification du système >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo Vérification des imprimantes disponibles... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo Vérification des outils PDF... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo if exist "data\PDFtoPrinter.exe" ( >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo     echo ✓ PDFtoPrinter disponible >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo     "data\PDFtoPrinter.exe" /? >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo ^) else ( >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo     echo ⚠ PDFtoPrinter manquant >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo ^) >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo if exist "data\SumatraPDF.exe" ( >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo     echo ✓ SumatraPDF disponible >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo ^) else ( >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo     echo ⚠ SumatraPDF manquant >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo ^) >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo echo Vérification terminée. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"
echo pause >> "%PACKAGE_DIR%\VERIFICATION_v1.5.6.bat"

REM Créer le fichier d'information
echo Création du fichier d'information...
echo SWAP Version 1.5.6 - Impression Automatique avec Outils Dédiés > "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ======================================== >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo Date de création: %date% %time% >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo PROBLÈME GHOSTSCRIPT RÉSOLU: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ============================ >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Plus d'interface Ghostscript qui s'ouvre >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Impression automatique MAINTENUE >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Utilise PDFtoPrinter (outil dédié) >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Fallback vers SumatraPDF >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Impression directe et fiable >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo OUTILS INCLUS: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ============== >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 1. PDFtoPrinter.exe - Outil d'impression PDF dédié >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 2. SumatraPDF.exe - Lecteur PDF léger (fallback) >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 3. Ces outils évitent complètement Ghostscript >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo MÉTHODES D'IMPRESSION: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ===================== >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 1. PDFtoPrinter.exe [fichier.pdf] [imprimante] - Priorité 1 >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 2. SumatraPDF.exe -print-to [imprimante] -silent [fichier.pdf] - Fallback >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 3. os.startfile [fichier.pdf] - Dernier recours (ouverture manuelle) >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo UTILISATION: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ============ >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 1. Scanner un SN >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 2. L'impression se fait automatiquement >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 3. Plus d'interface Ghostscript >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 4. Impression silencieuse et efficace >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo CORRECTIONS MAINTENUES: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ======================= >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Logique intelligente de sélection d'imprimante >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Priorité 1: HP LaserJet M109-M112 >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Priorité 2: EPSON ET contenant 2810 >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ Priorité 3: Imprimante par défaut (si non-POSTEK) >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ✓ POSTEK réservé uniquement aux étiquettes outbound >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo ============= >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 1. Copier tout le dossier SWAP_v1.5.6_Package sur le PC cible >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 2. Exécuter VERIFICATION_v1.5.6.bat pour vérifier le système >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.5.6.bat >> "%PACKAGE_DIR%\VERSION_1.5.6_INFO.txt"

REM Créer l'archive ZIP
echo Création de l'archive ZIP...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath 'SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.zip' -Force"

echo.
echo ========================================
echo ✓ PACKAGE CRÉÉ AVEC SUCCÈS !
echo ========================================
echo.
echo Dossier: %PACKAGE_DIR%
echo Archive: SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_OUTILS.zip
echo.
echo SOLUTION FINALE ANTI-GHOSTSCRIPT:
echo - Impression automatique avec outils dédiés
echo - PDFtoPrinter + SumatraPDF inclus
echo - Plus d'interface Ghostscript
echo - Impression directe et fiable
echo.
echo Le package est prêt pour le déploiement !
echo.
