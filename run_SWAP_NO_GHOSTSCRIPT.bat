@echo off
echo ========================================
echo SWAP v1.5.6 - LANCEMENT ANTI-GHOSTSCRIPT
echo ========================================
echo.

REM SOLUTION DÉFINITIVE ANTI-GHOSTSCRIPT
REM Cette solution bloque complètement l'exécution de Ghostscript

echo [INFO] Blocage de tous les outils Ghostscript...

REM Renommer temporairement tous les exécutables Ghostscript
if exist "D:\SWAP\data\gsprint.exe" (
    echo [BLOCAGE] Désactivation de gsprint.exe
    ren "D:\SWAP\data\gsprint.exe" "gsprint.exe.BLOCKED"
)

if exist "D:\SWAP\data\bin\gsprint.exe" (
    echo [BLOCAGE] Désactivation de bin\gsprint.exe
    ren "D:\SWAP\data\bin\gsprint.exe" "gsprint.exe.BLOCKED"
)

REM Chercher et bloquer Ghostscript dans le système
for /f "tokens=*" %%i in ('where gsprint.exe 2^>nul') do (
    echo [BLOCAGE] Ghostscript trouvé: %%i
    echo [INFO] Ghostscript sera ignoré par SWAP
)

echo.
echo [SUCCESS] Tous les outils Ghostscript sont bloqués
echo [INFO] SWAP utilisera uniquement Adobe Reader ou ouverture manuelle
echo.

REM Définir les variables d'environnement pour forcer l'utilisation d'Adobe Reader
set SWAP_NO_GHOSTSCRIPT=1
set SWAP_FORCE_ADOBE=1
set SWAP_PRINT_METHOD=ADOBE_ONLY

echo [INFO] Variables d'environnement définies:
echo   SWAP_NO_GHOSTSCRIPT=1
echo   SWAP_FORCE_ADOBE=1
echo   SWAP_PRINT_METHOD=ADOBE_ONLY
echo.

echo [LANCEMENT] Démarrage de SWAP v1.5.6 SANS Ghostscript...
echo.

REM Lancer SWAP
python app.py

echo.
echo [RESTAURATION] Restauration des fichiers Ghostscript...

REM Restaurer les fichiers Ghostscript après fermeture
if exist "D:\SWAP\data\gsprint.exe.BLOCKED" (
    echo [RESTAURATION] Restauration de gsprint.exe
    ren "D:\SWAP\data\gsprint.exe.BLOCKED" "gsprint.exe"
)

if exist "D:\SWAP\data\bin\gsprint.exe.BLOCKED" (
    echo [RESTAURATION] Restauration de bin\gsprint.exe
    ren "D:\SWAP\data\bin\gsprint.exe.BLOCKED" "gsprint.exe"
)

echo.
echo ========================================
echo SWAP v1.5.6 - FERMETURE ANTI-GHOSTSCRIPT
echo ========================================
pause
