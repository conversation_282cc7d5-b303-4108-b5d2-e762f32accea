@echo off
echo ========================================
echo SWAP v1.5.1 - Installation sur PC cible
echo ========================================
echo.

REM Vérifier si nous sommes dans le bon dossier
if not exist "SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe" (
    echo ERREUR: Fichier SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe non trouvé
    echo Assurez-vous d'être dans le bon dossier
    pause
    exit /b 1
)

echo Vérification du système...
echo.

REM Vérifier Python (optionnel pour les vérifications)
python --version >nul 2>&1
if errorlevel 1 (
    echo ATTENTION: Python n'est pas installé (optionnel pour cette application)
) else (
    echo ✓ Python détecté
)

REM Vérifier les imprimantes
echo.
echo Vérification des imprimantes disponibles...
echo.

REM Créer un script temporaire pour vérifier les imprimantes
echo import win32print > temp_check_printers.py
echo try: >> temp_check_printers.py
echo     printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)] >> temp_check_printers.py
echo     print("Imprimantes disponibles:") >> temp_check_printers.py
echo     for p in printers: >> temp_check_printers.py
echo         print(f"  - {p}") >> temp_check_printers.py
echo     print(f"\\nImprimante par défaut: {win32print.GetDefaultPrinter()}") >> temp_check_printers.py
echo     # Vérifier les imprimantes recommandées >> temp_check_printers.py
echo     hp_found = any("HP LaserJet M109-M112" in p for p in printers) >> temp_check_printers.py
echo     epson_found = any("EPSON" in p and "2810" in p for p in printers) >> temp_check_printers.py
echo     postek_found = any("POSTEK" in p for p in printers) >> temp_check_printers.py
echo     print("\\n--- ANALYSE ---") >> temp_check_printers.py
echo     if hp_found: >> temp_check_printers.py
echo         print("✓ HP LaserJet M109-M112 trouvée (PRIORITÉ 1)") >> temp_check_printers.py
echo     elif epson_found: >> temp_check_printers.py
echo         print("✓ EPSON ET-2810 trouvée (PRIORITÉ 2)") >> temp_check_printers.py
echo     else: >> temp_check_printers.py
echo         print("⚠ Ni HP LaserJet M109-M112 ni EPSON ET-2810 trouvées") >> temp_check_printers.py
echo         print("  L'application utilisera l'imprimante par défaut") >> temp_check_printers.py
echo     if postek_found: >> temp_check_printers.py
echo         print("✓ POSTEK trouvée (pour étiquettes outbound uniquement)") >> temp_check_printers.py
echo     else: >> temp_check_printers.py
echo         print("⚠ POSTEK non trouvée (étiquettes outbound non disponibles)") >> temp_check_printers.py
echo except Exception as e: >> temp_check_printers.py
echo     print(f"Erreur lors de la vérification: {e}") >> temp_check_printers.py

python temp_check_printers.py 2>nul
if errorlevel 1 (
    echo ATTENTION: Impossible de vérifier les imprimantes automatiquement
    echo Vérifiez manuellement que les imprimantes sont installées
)

del temp_check_printers.py 2>nul

echo.
echo ========================================
echo Installation terminée !
echo ========================================
echo.
echo Pour démarrer l'application:
echo 1. Double-cliquez sur run_SWAP_v1.5.1.bat
echo    OU
echo 2. Double-cliquez directement sur SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe
echo.
echo Pour vérifier les imprimantes:
echo - Exécutez VERIFICATION_v1.5.1.bat
echo.
echo NOTES IMPORTANTES:
echo - L'application sélectionnera automatiquement la meilleure imprimante
echo - HP LaserJet M109-M112 a la priorité la plus élevée
echo - EPSON ET-2810 est la deuxième priorité
echo - POSTEK est réservé aux étiquettes outbound uniquement
echo.
pause
