@echo off
echo ========================================
echo TEST FINAL DU PACKAGE SWAP v1.5.1
echo ========================================
echo.

REM Vérifier que le package existe
if not exist "SWAP_v1.5.1_Package" (
    echo ERREUR: Le dossier SWAP_v1.5.1_Package n'existe pas
    pause
    exit /b 1
)

echo ✓ Dossier package trouvé
echo.

REM Vérifier les fichiers essentiels
echo Vérification des fichiers essentiels...

if exist "SWAP_v1.5.1_Package\SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe" (
    echo ✓ Exécutable principal présent
) else (
    echo ✗ Exécutable principal MANQUANT
)

if exist "SWAP_v1.5.1_Package\data" (
    echo ✓ Dossier data présent
) else (
    echo ✗ Dossier data MANQUANT
)

if exist "SWAP_v1.5.1_Package\run_SWAP_v1.5.1.bat" (
    echo ✓ Script de lancement présent
) else (
    echo ✗ Script de lancement MANQUANT
)

if exist "SWAP_v1.5.1_Package\VERIFICATION_v1.5.1.bat" (
    echo ✓ Script de vérification présent
) else (
    echo ✗ Script de vérification MANQUANT
)

if exist "SWAP_v1.5.1_Package\VERSION_1.5.1_INFO.txt" (
    echo ✓ Fichier d'information présent
) else (
    echo ✗ Fichier d'information MANQUANT
)

if exist "SWAP_v1.5.1_Package\GUIDE_INSTALLATION_COMPLETE.txt" (
    echo ✓ Guide d'installation présent
) else (
    echo ✗ Guide d'installation MANQUANT
)

echo.

REM Vérifier l'archive ZIP
if exist "SWAP_v1.5.1_IMPRESSION_CORRIGEE.zip" (
    echo ✓ Archive ZIP créée
    echo Taille de l'archive:
    dir "SWAP_v1.5.1_IMPRESSION_CORRIGEE.zip" | find ".zip"
) else (
    echo ✗ Archive ZIP MANQUANTE
)

echo.
echo ========================================
echo RÉSUMÉ DU PACKAGE
echo ========================================
echo.
echo FICHIERS PRÊTS POUR LE DÉPLOIEMENT:
echo.
echo 📁 SWAP_v1.5.1_Package\
echo   ├── 🚀 SWAP_v1.5.1_IMPRESSION_CORRIGEE.exe
echo   ├── 📄 SumatraPDF.exe
echo   ├── 📂 data\ (ressources)
echo   ├── ▶️ run_SWAP_v1.5.1.bat
echo   ├── 🔍 VERIFICATION_v1.5.1.bat
echo   ├── 📋 VERSION_1.5.1_INFO.txt
echo   └── 📖 GUIDE_INSTALLATION_COMPLETE.txt
echo.
echo 📦 SWAP_v1.5.1_IMPRESSION_CORRIGEE.zip (archive complète)
echo.
echo ========================================
echo INSTRUCTIONS DE DÉPLOIEMENT
echo ========================================
echo.
echo 1. Copier l'archive ZIP sur le PC cible
echo 2. Extraire l'archive
echo 3. Exécuter install_on_target_pc_v1.5.1.bat
echo    OU suivre le GUIDE_INSTALLATION_COMPLETE.txt
echo.
echo ✅ CORRECTIONS INCLUSES:
echo   • Logique d'impression intelligente
echo   • HP LaserJet M109-M112 (Priorité 1)
echo   • EPSON ET-2810 (Priorité 2)
echo   • POSTEK réservé aux étiquettes outbound
echo.
pause
