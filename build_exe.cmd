@echo off
echo ===============================================
echo     CREATION DE L'EXECUTABLE SWAP v1.4.3
echo ===============================================
echo.

REM Installer PyInstaller si necessaire
pip install pyinstaller

REM Nettoyer les anciens builds
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

echo.
echo Construction de l'executable...
echo Cela peut prendre plusieurs minutes...

REM Creer l'executable avec PyInstaller
pyinstaller --onefile --windowed --name=SWAP_v1.4.3 --add-data=".env;." --add-data="data;data" app.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERREUR: La construction a echoue.
    pause
    exit /b 1
)

REM Creer le dossier de distribution
echo.
echo Creation du package de distribution...
if not exist "SWAP_Package" mkdir "SWAP_Package"

REM Copier l'executable
copy "dist\SWAP_v1.4.3.exe" "SWAP_Package\"

REM Copier les fichiers necessaires
if exist ".env" copy ".env" "SWAP_Package\"
if exist "data" xcopy "data" "SWAP_Package\data\" /E /I /Y
if exist "SumatraPDF.exe" copy "SumatraPDF.exe" "SWAP_Package\"

REM Creer un fichier README
echo SWAP Application v1.4.3 > "SWAP_Package\README.txt"
echo ======================== >> "SWAP_Package\README.txt"
echo. >> "SWAP_Package\README.txt"
echo INSTALLATION: >> "SWAP_Package\README.txt"
echo 1. Copiez tout le contenu de ce dossier sur l'autre PC >> "SWAP_Package\README.txt"
echo 2. Lancez SWAP_v1.4.3.exe >> "SWAP_Package\README.txt"
echo. >> "SWAP_Package\README.txt"
echo CONFIGURATION: >> "SWAP_Package\README.txt"
echo - Modifiez le fichier .env pour configurer la base de donnees >> "SWAP_Package\README.txt"
echo - Assurez-vous que les imprimantes sont installees >> "SWAP_Package\README.txt"

echo.
echo ===============================================
echo     CONSTRUCTION TERMINEE AVEC SUCCES !
echo ===============================================
echo.
echo L'executable se trouve dans: SWAP_Package\
echo Vous pouvez maintenant copier ce dossier sur l'autre PC.
echo.
pause
