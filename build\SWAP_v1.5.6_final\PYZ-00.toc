('D:\\SWAP\\build\\SWAP_v1.5.6_final\\PYZ-00.pyz',
 [('IPython',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files (x86)\\Python38-32\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files (x86)\\Python38-32\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Program Files (x86)\\Python38-32\\lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'C:\\Program Files (x86)\\Python38-32\\lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backcall',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\backcall\\__init__.py',
   'PYMODULE'),
  ('backcall._signatures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\backcall\\_signatures.py',
   'PYMODULE'),
  ('backcall.backcall',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\backcall\\backcall.py',
   'PYMODULE'),
  ('backports',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Program Files (x86)\\Python38-32\\lib\\base64.py',
   'PYMODULE'),
  ('bcrypt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bdb', 'C:\\Program Files (x86)\\Python38-32\\lib\\bdb.py', 'PYMODULE'),
  ('bisect',
   'C:\\Program Files (x86)\\Python38-32\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files (x86)\\Python38-32\\lib\\bz2.py', 'PYMODULE'),
  ('cProfile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\cProfile.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Program Files (x86)\\Python38-32\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\Program Files (x86)\\Python38-32\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chunk', 'C:\\Program Files (x86)\\Python38-32\\lib\\chunk.py', 'PYMODULE'),
  ('clr',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\clr.py',
   'PYMODULE'),
  ('clr_loader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\ffi\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi.hostfxr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\ffi\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.ffi.mono',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\ffi\\mono.py',
   'PYMODULE'),
  ('clr_loader.ffi.netfx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\ffi\\netfx.py',
   'PYMODULE'),
  ('clr_loader.hostfxr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.mono',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\mono.py',
   'PYMODULE'),
  ('clr_loader.netfx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\netfx.py',
   'PYMODULE'),
  ('clr_loader.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\types.py',
   'PYMODULE'),
  ('clr_loader.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\__init__.py',
   'PYMODULE'),
  ('clr_loader.util.clr_error',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\clr_error.py',
   'PYMODULE'),
  ('clr_loader.util.coreclr_errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\coreclr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.find',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\find.py',
   'PYMODULE'),
  ('clr_loader.util.hostfxr_errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\hostfxr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.runtime_spec',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\clr_loader\\util\\runtime_spec.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files (x86)\\Python38-32\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files (x86)\\Python38-32\\lib\\code.py', 'PYMODULE'),
  ('codeop',
   'C:\\Program Files (x86)\\Python38-32\\lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Program Files (x86)\\Python38-32\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files (x86)\\Python38-32\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files (x86)\\Python38-32\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files (x86)\\Python38-32\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files (x86)\\Python38-32\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files (x86)\\Python38-32\\lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files (x86)\\Python38-32\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files (x86)\\Python38-32\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Program Files (x86)\\Python38-32\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Program Files (x86)\\Python38-32\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files (x86)\\Python38-32\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Program Files (x86)\\Python38-32\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Program Files (x86)\\Python38-32\\lib\\decimal.py',
   'PYMODULE'),
  ('decorator',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('defusedxml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'C:\\Program Files (x86)\\Python38-32\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program Files (x86)\\Python38-32\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Program Files (x86)\\Python38-32\\lib\\doctest.py',
   'PYMODULE'),
  ('dotenv',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('exceptiongroup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('executing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing._pytest_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\_pytest_utils.py',
   'PYMODULE'),
  ('executing.executing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('filecmp',
   'C:\\Program Files (x86)\\Python38-32\\lib\\filecmp.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Program Files (x86)\\Python38-32\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Program Files (x86)\\Python38-32\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Program Files (x86)\\Python38-32\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Program Files (x86)\\Python38-32\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Program Files (x86)\\Python38-32\\lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'C:\\Program Files (x86)\\Python38-32\\lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Program Files (x86)\\Python38-32\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'C:\\Program Files (x86)\\Python38-32\\lib\\hmac.py', 'PYMODULE'),
  ('html',
   'C:\\Program Files (x86)\\Python38-32\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Program Files (x86)\\Python38-32\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Program Files (x86)\\Python38-32\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program Files (x86)\\Python38-32\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files (x86)\\Python38-32\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files (x86)\\Python38-32\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'C:\\Program Files (x86)\\Python38-32\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('importlib_resources._common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\_functional.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.compat.py38',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources.future',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('init_numpy', 'D:\\SWAP\\init_numpy.py', 'PYMODULE'),
  ('inspect',
   'C:\\Program Files (x86)\\Python38-32\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Program Files (x86)\\Python38-32\\lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('jedi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Program Files (x86)\\Python38-32\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files (x86)\\Python38-32\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files (x86)\\Python38-32\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files (x86)\\Python38-32\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files (x86)\\Python38-32\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Program Files (x86)\\Python38-32\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program Files (x86)\\Python38-32\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files (x86)\\Python38-32\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\cbook\\__init__.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Program Files (x86)\\Python38-32\\lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('mysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\__init__.py',
   'PYMODULE'),
  ('mysql.connector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\abstracts.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\authentication.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\connection.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\conversion.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\cursor.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\custom_types.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\dbapi.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\errorcode.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\errors.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\locales\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.locales.eng',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\locales\\eng\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.logger',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\logger.py',
   'PYMODULE'),
  ('mysql.connector.network',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\network.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\opentelemetry\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\opentelemetry\\constants.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.context_propagation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\opentelemetry\\context_propagation.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.instrumentation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\opentelemetry\\instrumentation.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\optionfiles.py',
   'PYMODULE'),
  ('mysql.connector.plugins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\plugins\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.plugins.caching_sha2_password',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\plugins\\caching_sha2_password.py',
   'PYMODULE'),
  ('mysql.connector.plugins.mysql_native_password',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\plugins\\mysql_native_password.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\pooling.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\protocol.py',
   'PYMODULE'),
  ('mysql.connector.tls_ciphers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\tls_ciphers.py',
   'PYMODULE'),
  ('mysql.connector.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\types.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\utils.py',
   'PYMODULE'),
  ('mysql.connector.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\mysql\\connector\\version.py',
   'PYMODULE'),
  ('nacl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE'),
  ('nacl.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE'),
  ('netbios',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files (x86)\\Python38-32\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files (x86)\\Python38-32\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Program Files (x86)\\Python38-32\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Program Files (x86)\\Python38-32\\lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Program Files (x86)\\Python38-32\\lib\\optparse.py',
   'PYMODULE'),
  ('outcome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('parso',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'C:\\Program Files (x86)\\Python38-32\\lib\\pdb.py', 'PYMODULE'),
  ('pdfkit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pdfkit\\__init__.py',
   'PYMODULE'),
  ('pdfkit.api',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pdfkit\\api.py',
   'PYMODULE'),
  ('pdfkit.configuration',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pdfkit\\configuration.py',
   'PYMODULE'),
  ('pdfkit.pdfkit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pdfkit\\pdfkit.py',
   'PYMODULE'),
  ('pdfkit.source',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pdfkit\\source.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pickle.py',
   'PYMODULE'),
  ('pickleshare',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pickleshare.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Program Files (x86)\\Python38-32\\lib\\platform.py',
   'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pprint.py',
   'PYMODULE'),
  ('profile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\profile.py',
   'PYMODULE'),
  ('prompt_toolkit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pstats.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pure_eval',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files (x86)\\Python38-32\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files (x86)\\Python38-32\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythonnet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pythonnet\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files (x86)\\Python38-32\\lib\\queue.py', 'PYMODULE'),
  ('quopri',
   'C:\\Program Files (x86)\\Python38-32\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Program Files (x86)\\Python38-32\\lib\\random.py',
   'PYMODULE'),
  ('reportlab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\reportlab\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\reportlab\\lib\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib.pagesizes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\reportlab\\lib\\pagesizes.py',
   'PYMODULE'),
  ('reportlab.lib.units',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\reportlab\\lib\\units.py',
   'PYMODULE'),
  ('reportlab.pdfgen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\reportlab\\pdfgen\\__init__.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files (x86)\\Python38-32\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files (x86)\\Python38-32\\lib\\runpy.py', 'PYMODULE'),
  ('secrets',
   'C:\\Program Files (x86)\\Python38-32\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Program Files (x86)\\Python38-32\\lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files (x86)\\Python38-32\\lib\\shlex.py', 'PYMODULE'),
  ('shutil',
   'C:\\Program Files (x86)\\Python38-32\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Program Files (x86)\\Python38-32\\lib\\signal.py',
   'PYMODULE'),
  ('site', 'C:\\Program Files (x86)\\Python38-32\\lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\smtplib.py',
   'PYMODULE'),
  ('sniffio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\Program Files (x86)\\Python38-32\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program Files (x86)\\Python38-32\\lib\\socketserver.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sqlalchemy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files (x86)\\Python38-32\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files (x86)\\Python38-32\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files (x86)\\Python38-32\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files (x86)\\Python38-32\\lib\\ssl.py', 'PYMODULE'),
  ('stack_data',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('string',
   'C:\\Program Files (x86)\\Python38-32\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program Files (x86)\\Python38-32\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files (x86)\\Python38-32\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program Files (x86)\\Python38-32\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Program Files (x86)\\Python38-32\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Program Files (x86)\\Python38-32\\lib\\threading.py',
   'PYMODULE'),
  ('timeit',
   'C:\\Program Files (x86)\\Python38-32\\lib\\timeit.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'C:\\Program Files (x86)\\Python38-32\\lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('trio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files (x86)\\Python38-32\\lib\\tty.py', 'PYMODULE'),
  ('typing',
   'C:\\Program Files (x86)\\Python38-32\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files (x86)\\Python38-32\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files (x86)\\Python38-32\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files (x86)\\Python38-32\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files (x86)\\Python38-32\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files (x86)\\Python38-32\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files (x86)\\Python38-32\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\Program Files (x86)\\Python38-32\\lib\\uuid.py', 'PYMODULE'),
  ('wave', 'C:\\Program Files (x86)\\Python38-32\\lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files (x86)\\Python38-32\\lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xlsxwriter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Program Files (x86)\\Python38-32\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files (x86)\\Python38-32\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Program Files (x86)\\Python38-32\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
