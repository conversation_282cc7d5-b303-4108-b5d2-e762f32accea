try:
    import init_numpy
    init_numpy.init()
except ImportError:
    pass

from tkinter import *
import mysql.connector
import pandas as pd
from datetime import datetime, timedelta ,date
from tkinter import messagebox 
import subprocess , pdfkit , win32print
from tkinter.ttk import Combobox
import os 
from tkinter import Tk, PhotoImage
from PIL import Image, ImageTk
import sys 
import tempfile
import shutil,win32api

import time
from dotenv import load_dotenv
import os

# Charger les variables d'environnement depuis le fichier .env
dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

model_counts = None 
# Récupérer les variables d'environnement
port1 = os.getenv('DB_PORT', '3306')
try:
    DB_PORT = int(port1)
except (ValueError, TypeError):
    raise ValueError("DB_PORT must be a number in .env")
HostIp = os.getenv('DB_HOST_IP')
User = os.getenv('DB_USER')
Password = os.getenv('DB_PASSWORD')
DataBaseName = os.getenv('DB_NAME')

# Affichage formaté
print(f"Port: {port1}")
print(f"Host IP: {HostIp}")
print(f"User: {User}")
print(f"Password: {Password}")
print(f"Database Name: {DataBaseName}")

window = Tk()
window.title('SWAP Version_1.4.3')

print("***************SWAP Version_1.4.3*****************")

window.geometry("600x600")

base_path = 'D:\SWAP'

print('*********** l path base  ***********',base_path)

# Construct the path to the image file relative to the executable
icon_path = os.path.join(base_path, "data", "exchange.png")

# Open the image with Pillow
image = Image.open(icon_path)

# Convert the image to a format supported by PhotoImage
icon_image = ImageTk.PhotoImage(image)

# Change the window icon
window.iconphoto(True, icon_image)
#window.configure(bg="#FFF0F5")


title_label = Label(window, text="SWAP Models PS5 : ", font="Arial 20 bold")
title_label.grid(row=0, column=0, pady=10)

snnew = None
peripherique= None
id_model = None
outbound_label = None
last_pdf_path = None
last_printer_name = None

df = pd.DataFrame()

def get_capabilities_path():
    #script_dir = os.path.dirname(os.path.abspath(__file__))
    script_dir = 'D:\SWAP'
    capabilities_path = os.path.join(script_dir, "data", "capabilities.json")

    if getattr(sys, 'frozen', False):
        # Running in a bundle (PyInstaller)
        #temp_dir = sys._MEIPASS
        temp_dir = 'D:\SWAP'
        print('temp_dir',temp_dir)
        temp_capabilities_dir = os.path.join(temp_dir, "escpos", "capabilities")

        print('temp_capabilities_dir1',temp_capabilities_dir)

        # Ensure the directory structure exists in the temporary directory
        if not os.path.exists(temp_capabilities_dir):
            os.makedirs(temp_capabilities_dir)

        temp_capabilities_path = os.path.join(temp_capabilities_dir, "capabilities.json")

        print('temp_capabilities_path2',temp_capabilities_path)

        # Copy the capabilities file to the temporary directory if it doesn't exist
        if not os.path.exists(temp_capabilities_path):
            shutil.copy(capabilities_path, temp_capabilities_path)

        return temp_capabilities_path

    return capabilities_path
 
CAPABILITIES_PATH = get_capabilities_path()
print('CAPABILITIES_PATH',CAPABILITIES_PATH)

#changing paths 
#script_dir = os.path.dirname(os.path.abspath(__file__))
script_dir = 'D:\SWAP'

bin_script = 'D:\SWAP\data'
GSPRINT_PATH = os.path.join(script_dir, "data", "gsprint.exe")
GHOSTSCRIPT_PATH = os.path.join(bin_script, "bin", "gswin64.exe")

WKHTMLTOPDF_PATH = r'D:\SWAP\data\wkhtmltopdf\bin\wkhtmltopdf.exe'


print("GSPRINT_PATH:", GSPRINT_PATH)
print("GHOSTSCRIPT_PATH:", GHOSTSCRIPT_PATH)
print("wkhtmltopdf_PATH:", WKHTMLTOPDF_PATH)

Acrobat_PATH = os.path.join(script_dir, "data", "Acrobat.exe")

def get_outbound_label(snOld):
    print('get_outbound_label snOld',snOld)
    #create_insert_holidays()
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    #conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    try:
        query = "SELECT outbound_label FROM sn WHERE snOld = %s ORDER BY date DESC LIMIT 1 "
        cursor.execute(query, (snOld,))

        # Fetch all the rows as a list of tuples
        data = cursor.fetchone()
        #print('*data*',data)
        return data[0]

    finally:
        # Close the database connection in a finally block to ensure it happens even if an exception occurs
        cursor.close()
        conn.close()


def get_models_swap():

    global snnew , peripherique

    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()

    try:

        query = "SELECT sn.idsn, sn.snold, sn.movement_idnextjob, sn.model_idmodel, sn.date, model.model, GROUP_CONCAT(CONCAT_WS(' (', peripherique.name, CONCAT('Quantity: ', peripherique.Quantity, ')')) SEPARATOR '; ') AS peripherals FROM sn JOIN model ON sn.model_idmodel = model.idmodel LEFT JOIN peripherique ON peripherique.sn_idsn = sn.idsn JOIN ( SELECT snold, MAX(date) AS max_date FROM sn GROUP BY snold ) AS latest ON sn.snold = latest.snold AND sn.date = latest.max_date WHERE sn.snold IS NOT NULL AND model.model LIKE '%CFI-%' AND latest.snold IS NOT NULL AND sn.movement_idnextjob = 106 GROUP BY sn.snold; "
        cursor.execute(query)

        # Fetch all the rows as a list of tuples
        data = cursor.fetchall()

        # Convert the data to a DataFrame
        column_names = [desc[0] for desc in cursor.description]
        df = pd.DataFrame(data, columns=column_names)

        print('*********')
        print(df)
        #quit()
        return df
    except mysql.connector.Error as e:
        print("Error get_models_swap : ", e)
        
    finally:

        cursor.close()
        conn.close()


def create_labels(column1, column2):
    global df,model_counts
    # Clear previous labels (excluding entry_snnew)
    for widget in window.winfo_children():
        if isinstance(widget, Frame):
            widget.destroy()

    labels_frame = Frame(window, bg="#FFF0F5")
    labels_frame.grid(row=1, column=0, pady=10)
    
    df = get_models_swap()
    df["model"] = df["model"].str.replace(' ','')
    print('--df[mode]--')
    print(df)

    model_counts = df.groupby('model').size().reset_index(name='quantity')

    # Display the new DataFrame
    print("**model_counts**")
    print(model_counts)
    

    for index, (value1, value2) in enumerate(zip(model_counts[column1], model_counts[column2])):
        label = Label(labels_frame, text=f" {value1} : {value2}", font="Arial 16 bold", fg="#00008B", bg="#EEE8AA")
        label.grid(row=index, column=0, pady=5)

    label_snnew = Label(labels_frame, text="Give snnew to swap :", font="Arial 14 bold")
    label_snnew.grid(row=len(model_counts) , column=0, pady=5)
    #label_snnew.grid(row=len(model_counts)//2, column=21, pady=5)

    entry_snnew.grid(row=len(model_counts) + 10 , column=0, pady=5)
    entry_snnew.bind("<Return>", get_snnew)
    #entry_snnew.grid(row=len(model_counts)//2 + 1 , column=21, pady=5)

    # Schedule the function to run again after 3000 milliseconds (3 seconds)
    window.after(4000, lambda: create_labels(column1, column2))


def get_rma(snOld):

    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()
    try:
        query = "SELECT RMA_Number FROM sn WHERE snOld = %s and  movement_idnextjob = 106 ORDER BY date DESC LIMIT 1"
        cursor.execute(query, (snOld,))
        # Fetch all the rows as a list of tuples
        data = cursor.fetchone()
        return data
    finally:
        # Close the database connection in a finally block to ensure it happens even if an exception occurs
        cursor.close()
        conn.close()



def update_snnew_data(snOld,snnew,rma,CycleonId,id_model):
    # Create a connection to the MySQL database
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()
    print('id_model from update_snnew_data',id_model)
    length = len(str(id_model))
    if isinstance(id_model, tuple):
        id_model = id_model[0]
    else:
        print("2")
        # If it's not a tuple, assume it's already the desired model value
        id_model = id_model

    print('id_model',id_model)

    try: 

        print('CycleonId from update_snnew_data function : ',CycleonId)
        current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        update_query = "UPDATE sn SET snnew = %s, OutboundCycleonID = %s, date = %s,date_packaging_first = %s, date_packaging = %s,  movement_idnextjob = 21,  movement_idmovement = 106 ,model_idmodel = %s WHERE snOld = %s ORDER BY idsn DESC LIMIT 1  "

        #cursor.execute(update_query, (snnew, str(CycleonId), current_datetime, current_datetime, current_datetime, id_model, snOld,))
        cursor.execute(update_query, (*(snnew, str(CycleonId), current_datetime, current_datetime, current_datetime, id_model, snOld,),))
        insert_query = "INSERT INTO sn_movement (sn_idsn, sn_idacc, Job_Id, snOld , snNew , RMA_Number ,date,movement_idmovement,movement_idnextjob ,my_aspnet_users_id,model_idmodel) VALUES ((select idsn from sn where snold = %s ORDER BY date DESC LIMIT 1 ),0,(select Job_Id from sn where snold = %s ORDER BY date DESC LIMIT 1),%s,%s,%s,NOW(),106,21, (select id from my_aspnet_users where name = 'SwapApp'), %s  )"
        cursor.execute(insert_query,(snOld,snOld,snOld,snnew,rma,id_model,))
        
        #update the SC05Description value to get the sc05 file complete 
        sc05_val = 'Refurbished replacement'
        update_sc05_query = "UPDATE sn SET SC05Description = %s WHERE snold = %s ORDER BY idsn DESC LIMIT 1 "
        cursor.execute(update_sc05_query,(sc05_val,snOld))

        conn.commit()
        text_variable.set("Sucess SWAP for " + snnew)
        label.config(bg="green",fg = "white")

        entry_snnew.config(state= "normal")
        entry_snnew.delete(0,'end')
        entry_snnew.focus_set()
        print("Data updated successfully.")
        
    except mysql.connector.Error as err:
        text_variable.set(err)
        print(f"Error1: {err}")
        
    finally:
        # Close the cursor and connection
        cursor.close()
        conn.close()


def get_model_productcode():
    print('get_model_productcode')
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    #conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    try:

        query = "SELECT * FROM swap_model "
        cursor.execute(query)

        # Fetch all the rows as a list of tuples
        data = cursor.fetchall()
        # Get the column names
        column_names = [desc[0] for desc in cursor.description]

        # Create a DataFrame from the fetched data
        df = pd.DataFrame(data, columns=column_names)
        return df
    finally:
        # Close the database connection in a finally block to ensure it happens even if an exception occurs
        cursor.close()
        conn.close()

def get_model_productcode_pro():
    print('get_model_productcode')
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    #conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    try:

        query = "SELECT * FROM swap_model where ModelNumber like '%7016%' "
        cursor.execute(query)

        # Fetch all the rows as a list of tuples
        data = cursor.fetchall()
        # Get the column names
        column_names = [desc[0] for desc in cursor.description]

        # Create a DataFrame from the fetched data
        df = pd.DataFrame(data, columns=column_names)
        return df
    finally:
        # Close the database connection in a finally block to ensure it happens even if an exception occurs
        cursor.close()
        conn.close()



def get_model_choice(models):
    result_var = None # StringVar to store the selected value

    def on_ok():
        #window.destroy()
        selected_value = combo.get()
        print("Selected Value:", selected_value)
        combo.grid_forget()
        ok_button.grid_forget()
    combo = Combobox(window, values=models, textvariable=result_var) 
    combo.grid(row=30, column=10, padx=10, pady=10)

    ok_button = Button(window, text="OK", command=on_ok)
    ok_button.grid(row=40, column=10, padx=10, pady=10)

    return result_var


def create_pdf_outbound_label(file_bytes, output_path, printer_name):
    # Assuming you have defined GSPRINT_PATH and GHOSTSCRIPT_PATH somewhere

    if file_bytes is not None:
        temp_pdf_file_path = output_path

        try:
            # Save PDF to a temporary file
            with open(temp_pdf_file_path, 'wb') as file_stream:
                file_stream.write(file_bytes)

            # Create a temporary copy of the PDF file
            temp_print_file = tempfile.mktemp(suffix='.pdf')
            shutil.copy(temp_pdf_file_path, temp_print_file)
            # Calculate dimensions in points (1 inch = 72 points)

            #width_mm = 10
            width_mm = 8
            height_mm = 15
            
            width_points = width_mm * 28.35 # Convert mm to points
            height_points = height_mm * 28.35   # Convert mm to points

            # Construct the Ghostscript command

            # Construct the Ghostscript command
            command = [
                GSPRINT_PATH,
                '-ghostscript', GHOSTSCRIPT_PATH,
                '-printer', printer_name,
                '-dPDFFitPage',  # Fit the PDF to the page size
                '-dFIXEDMEDIA',  # Use fixed media size (if needed)
                f'-dDEVICEWIDTHPOINTS={width_points}',  # Specify the width in points
                f'-dDEVICEHEIGHTPOINTS={height_points}',  # Specify the height in points
                temp_print_file
            ]
            subprocess.check_call(command)

        except Exception as ex:
            print("**Error:", ex)
        finally:
            win32print.SetDefaultPrinter(printer_name)

def convert_to_pdf(html_file_name, model_value, sn_value, rma_value, today_value, snold):
    global outbound_label
    try:
        
        print('(1)')
        # Read HTML content
        with open(html_file_name, 'r', encoding='utf-8') as temp_html_file:
            html_content = temp_html_file.read()

        # Replace placeholders with actual values
        model_value = model_value
        html_content = html_content.replace('{{ model }}', model_value)
        html_content = html_content.replace('{{ sn }}', sn_value)
        html_content = html_content.replace('{{ rma }}', rma_value)
        html_content = html_content.replace('{{ current_date }}', today_value)
        default_printer = win32print.GetDefaultPrinter()

        # Obtenir la liste des imprimantes disponibles
        available_printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
        print('Available printers:', available_printers)

        # Logique de sélection d'imprimante pour PDF principal
        printer_name = None

        # 1. Chercher HP LaserJet M109-M112
        for printer in available_printers:
            if "HP LaserJet M109-M112" in printer:
                printer_name = printer
                print('Selected HP LaserJet M109-M112:', printer_name)
                break

        # 2. Si pas trouvée, chercher EPSON avec 2810
        if printer_name is None:
            for printer in available_printers:
                if "EPSON" in printer and "2810" in printer:
                    printer_name = printer
                    print('Selected EPSON 2810:', printer_name)
                    break

        # 3. Si aucune des deux n'est trouvée, utiliser l'imprimante par défaut (sauf si c'est POSTEK)
        if printer_name is None:
            if "POSTEK" not in default_printer:
                printer_name = default_printer
                print('Using default printer:', printer_name)
            else:
                # Si l'imprimante par défaut est POSTEK, chercher une autre imprimante
                for printer in available_printers:
                    if "POSTEK" not in printer:
                        printer_name = printer
                        print('Default is POSTEK, using alternative:', printer_name)
                        break

        # Si aucune imprimante appropriée n'est trouvée, utiliser la première non-POSTEK
        if printer_name is None:
            for printer in available_printers:
                if "POSTEK" not in printer:
                    printer_name = printer
                    print('Using first non-POSTEK printer:', printer_name)
                    break

        print('Final selected printer for PDF.... *****-----++++ ',printer_name)
        print('(4)')
        # Save HTML content to a temporary file
        temp_html_path = r'D:\SWAP\temps1.html'
        with open(temp_html_path, 'w', encoding='utf-8') as temp_html_file:
            print('(5)')
            temp_html_file.write(html_content)
        try:
            print('(6)')
            extension = '.pdf'

            #pdf_path = rf"D:\SWAP\{sn_value}_{extension}"
            pdf_path = rf"D:\SWAP\{sn_value}_{extension}"

            #pdf_path = f'{sn_value}.pdf'
            print("temp_html_path",temp_html_path)

            print('pdf_path',pdf_path)
            print('WKHTMLTOPDF_PATH',WKHTMLTOPDF_PATH)
            # Convert HTML to PDF using pdfkit
            try:
                config = pdfkit.configuration(wkhtmltopdf=r'D:\SWAP\data\bin\wkhtmltopdf.exe')  # Provide the actual path to wkhtmltopdf.exe

                # Convert HTML to PDF
                pdfkit.from_file(temp_html_path, pdf_path, configuration=config)
                print("PDF conversion successful!")
                if os.path.exists(pdf_path):
                    print("PDF file exists at:", pdf_path)
                else:
                    print("PDF file does not exist at:", pdf_path)
            except Exception as e:
                print("Error:", e)
                messagebox.showerror('err',str(e))

        except Exception as ex:
            print("**Error during PDF conversion:", ex)

        # IMPRESSION AUTOMATIQUE DIRECTE (évite Ghostscript)
        win32print.SetDefaultPrinter(printer_name)
        print('(9)')
        print('printer_name sélectionnée pour impression:',printer_name)

        # Stocker les informations d'impression
        global last_pdf_path, last_printer_name
        last_pdf_path = pdf_path
        last_printer_name = printer_name

        try:
            # IMPRESSION NATIVE ANTI-GHOSTSCRIPT
            print('=== IMPRESSION NATIVE ANTI-GHOSTSCRIPT ===')
            print(f'✓ PDF créé: {pdf_path}')
            print(f'✓ Imprimante sélectionnée: {printer_name}')

            impression_reussie = False

            # Méthode 1: Adobe Reader (priorité 1 - le plus fiable)
            adobe_paths = [
                r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
                r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
                r"C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
                r"C:\Program Files (x86)\Adobe\Reader 11.0\Reader\AcroRd32.exe"
            ]

            for adobe_path in adobe_paths:
                if os.path.exists(adobe_path):
                    try:
                        print(f'Impression avec Adobe Reader: {adobe_path}')

                        # Utiliser le paramètre /t pour impression silencieuse
                        result = subprocess.run([
                            adobe_path,
                            '/t',  # Impression silencieuse
                            pdf_path,
                            printer_name
                        ], timeout=30, creationflags=subprocess.CREATE_NO_WINDOW)

                        print('✓ Impression Adobe Reader lancée')
                        impression_reussie = True
                        break

                    except Exception as e:
                        print(f'⚠ Adobe Reader échoué: {e}')
                        continue

            # Méthode 2: win32api printto (fallback fiable)
            if not impression_reussie:
                try:
                    print('Impression avec win32api printto...')
                    win32api.ShellExecute(0, "printto", pdf_path, f'"{printer_name}"', ".", 0)
                    print('✓ Impression win32api réussie')
                    impression_reussie = True
                except Exception as e:
                    print(f'⚠ Erreur win32api: {e}')

            # Méthode 3: Commande Windows print native
            if not impression_reussie:
                try:
                    print('Impression avec commande Windows native...')
                    result = subprocess.run([
                        'cmd', '/c', 'print', f'/D:{printer_name}', pdf_path
                    ], capture_output=True, text=True, timeout=30,
                      creationflags=subprocess.CREATE_NO_WINDOW)

                    if result.returncode == 0:
                        print('✓ Impression commande Windows réussie')
                        impression_reussie = True
                    else:
                        print(f'⚠ Commande Windows échouée: {result.stderr}')
                except Exception as e:
                    print(f'⚠ Commande Windows erreur: {e}')

            # Méthode 4: Ouverture simple (dernier recours)
            if not impression_reussie:
                try:
                    print('Ouverture du PDF pour impression manuelle...')
                    # IMPORTANT: Utiliser startfile SANS "print" pour éviter Ghostscript
                    os.startfile(pdf_path)
                    print('✓ PDF ouvert - Utilisez Ctrl+P pour imprimer')
                    impression_reussie = True
                except Exception as e:
                    print(f'⚠ Erreur ouverture PDF: {e}')

            if impression_reussie:
                print('✅ IMPRESSION TERMINÉE AVEC SUCCÈS (AUCUN GHOSTSCRIPT)')
            else:
                print('❌ Toutes les méthodes d\'impression ont échoué')

        except subprocess.TimeoutExpired:
            print('⚠ Timeout impression, tentative alternative...')
            # Méthode 3: Impression via copie directe vers imprimante
            try:
                import shutil
                printer_path = f"\\\\localhost\\{printer_name}"
                print(f'Tentative copie directe vers {printer_path}')
                # Cette méthode ne fonctionne que pour certaines imprimantes
                # mais évite complètement Ghostscript
            except Exception as ex3:
                print(f'⚠ Copie directe échouée: {ex3}')

        except Exception as ex:
            print(f'⚠ Erreur impression: {ex}')
            print('Ouverture du PDF pour impression manuelle...')
            try:
                # Méthode de dernier recours: ouvrir le PDF (SANS "print" pour éviter Ghostscript)
                os.startfile(pdf_path)
                print('✓ PDF ouvert - Utilisez Ctrl+P pour imprimer')
            except Exception as ex2:
                print(f'✗ Impossible d\'ouvrir le PDF: {ex2}')

        print('(11)')
    except Exception as ex:
        print("**Error_1:", ex)
        #messagebox.showerror('err',str(ex))


    print('(12)')
    extension = '.pdf' 
    outbound_label = get_outbound_label(snold)
 
    current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f'{"outbound"}_{snnew}{extension}'
    printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
    print('(13)')
    for printer_name in printers:
        print('(14)')
        print('printer_name',printer_name)
        print_options = {
            "PrinterName": printer_name,
            "OutputFile": output_path,
        }
        print(':) printer_name :)',printer_name)


        if "POSTEK" in printer_name :
            print(f"Type of outbound_label: {type(outbound_label)}")
            create_pdf_outbound_label(outbound_label, output_path,printer_name)

            print('(15)')
            print('+++printer_name+++ POSTEK in ',printer_name)



def verif_snnew(snnew):
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()

    #verro snnew exist:
    select_query = "select snold from sn where movement_idmovement = 106 and movement_idnextjob = 21 and snnew = %s"
    cursor.execute(select_query, (snnew,))
    #result = cursor.fetchall()
    result = cursor.fetchone()

    return result


def verif_nextjob_mvt_snold_de_snnew(snnew):
    #F43401TYJ10400129
    re = False
    print('snnew 1: ',snnew)
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()
    select_sn_query = "select snold from sn where snNew = %s ORDER BY idsn DESC LIMIT 1 ; "
    cursor.execute(select_sn_query, (snnew,) )
    snold = cursor.fetchone()
    print(" 1 snold from verif_nextjob_mvt_snold_de_snnew is",snold)

    if snold is not None : 
        print('snold not none :) ')
        snold = snold[0]
        print("snold",snold)

        select_nxtjob_query = "select movement_idmovement,movement_idnextjob from sn where snold = %s ORDER BY idsn DESC LIMIT 1 ; "
        cursor.execute(select_nxtjob_query, (snold,))
        nextjob = cursor.fetchone()
        if nextjob is not None:

            print("Next job details:", nextjob)
            if nextjob == (13, 13):
                re = True
            else : 
                re = "Next job and host are not Packaging."
                print('//nextjob is not (13,13) it is ',nextjob)
        else:
            re = "No next job found for snold"
            print("No next job found for snold:", snold)
    else : 
        print('snold none :) ')
        re = "no snold found for the snnew"
        
    return re



def get_lang_fromSNNEW(snold):

    print('snold 1: ',snold)
    conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
    cursor = conn.cursor()
    select_sn_query = "select idsn from sn where snold = %s  ORDER BY idsn DESC LIMIT 1 ;"
    cursor.execute(select_sn_query, (snold,) )
    idsn = cursor.fetchone()
    print(" idsn iss ",idsn)
    idsn = idsn[0]  # extrait la valeur réelle de l'ID
    print(" idsn iss ",idsn)
    if idsn is not None : 
        print('idsn not none :) ')
        query_lang = "select sub_family.col1 as country from sn_has_sub_family  inner join sub_family on sub_family.id_sub_family = sn_has_sub_family.sub_family_id_sub_family where sub_family.family_idfamille =5 and sn_has_sub_family.sn_idsn = %s LIMIT 1"
        cursor.execute(query_lang, (idsn,))
        lang = cursor.fetchone()
        if lang is not None:
            lang = lang[0]
            print("Lang details:", lang)
            re = lang
        else:
            re = "No next job found for snold"

    else : 
        print('idsn none :) ')
        re = "no idsn found for the snnew"
        
    return re



def get_snnew(e):

    global snnew ,df,model_counts,cyclonid,snOld,rma,id_model, outbound_label, peripherique
    #snnew = entry_snnew.get()
    snnew = entry_snnew.get().replace(" ", "")

    entry_snnew.config(state= "disabled")

    print("snnew 2 : ",snnew)

    print('lenght of snnew ',len(snnew))

    if len(snnew) >= 17 :
        label.config(bg="green",fg = "white")
        print('good')
        #text_variable.set("good ! ")
        result = verif_snnew(snnew)
        print('**result psss **')
        print(result)


        if result is not None:
            print(result)
            label.config(bg="#FF0800",fg = "white")
            entry_snnew.config(state= "normal")
            entry_snnew.delete(0,'end')
            text_variable.set('Console ' + snnew +'  is already used !')

        else :
            #dataframe from swap_model table
            df_product_code = get_model_productcode()
            df_product_code_pro = get_model_productcode_pro()


            # Select the SCEIProductCode column
            valeurs_permises = df_product_code['SCEIProductCode'].tolist()
            valeurs_permises_pro = df_product_code_pro['SCEIProductCode'].tolist()

            # Print the list of SCEIProductCode values
            print(valeurs_permises)
            print('---')
            print(valeurs_permises_pro)
            extr = snnew[6:9]
            print('******extr*****')
            print(extr)

            #verifier le prefix si lindice 7 de snnew est dans la valeurs_permises
            if any(valeur in extr for valeur in valeurs_permises) :
                print('hello maybe pro')

                snold_nxtjob = verif_nextjob_mvt_snold_de_snnew(snnew)
                print('snold_nxtjob',snold_nxtjob)

                if snold_nxtjob == True :
                    print("**La valeur est valide.")
                    print('*****')
                    print(df_product_code)

                    for index, row in df_product_code.iterrows():

                        value = row['SCEIProductCode']

                        #print('value',value)

                        if (value in snnew) :
                            print('00_contain')
                            ModelNumber = row['ModelNumber']
                            print('ModelNumber de snnew1 = ',ModelNumber)

                            id_model = get_id_model(ModelNumber)
                            print('*id_model : ',id_model)



                            if id_model :
                                id_model = id_model[0]
                                if isinstance(id_model, tuple):
                                    print('cest une istance ! ')
                                    id_model = id_model[0]

                                    print('ID Model:', id_model)

                                print('1 :) ',id_model)

                            else :
                                print('----------------------------')
                                print('idmodel vide :) ')
                                print('2 :) ')

                            print('**id_model[0] : ',id_model)


                            print(f"La valeur {snnew} contient {row['SCEIProductCode']} a été trouvée à l'index {index} with Chassis = {row['Chassis']} with model = {row['ModelNumber']}")

                            chassis = row['Chassis']
                            model = row['ModelNumber']

                            print('chassis : ',chassis)
                            print('model : ',model)


                            df['model'] = df['model'].str.strip()

                            print('the first dataframe df ')
                            print(df)



                            models_list = df['model']
                            models_list = models_list.str.replace(' ', '')

                            models_list = models_list.to_string(index=False)

                            print('models_list--',models_list)

                            print('model',model)
                            filtered_df = df.loc[df['model'] == model]
                            print('hy filtered_df ')
                            print(filtered_df)
                            print('df')
                            print(df)

                            print('++2++')
                            if filtered_df.empty:

                                print('model here ****: ', model)



                                if '1016A' in model :
                                    id_model = "930"
                                    print('if "1016A" in model :',model)

                                    #1016A  #1016B ?

                                    val1 = "1016A"
                                    val2 = "1016B"

                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()
                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()

                                    if contain_val1 :
                                        print('val1[4:]',val1[4:])
                                        print('raw model contain val1')

                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                        print(filtered_df)
                                        print('swap to 1016A')
                                    elif contain_val2 :
                                        #id_model = "936"
                                        print('01')
                                        print('raw model contain val2')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]
                                        print('swap to 1016B')

                                    else :
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        return

                                elif '1116A' in model :
                                    #ok
                                    #1116A 1116B 1016A 1016B

                                    print('snnew have 1116A as model ')
                                    print('elif "1116A" in model :')
                                    id_model = "936"

                                    val0 = "1116A"
                                    val1 = "1116B"
                                    val2 = "1016A"
                                    val3 = "1016B"

                                    contain_val0 = df['model'].str.contains(val0, case= False, na=False).any()
                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()
                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()
                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()

                                    if contain_val0 :

                                        print('10')
                                        print('raw model contain val2')
                                        filtered_df = df[df['model'].str.contains(val0, case=False, na=False)]
                                        print('swap to 1116A')
                                    elif contain_val1 :

                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                    elif contain_val2 :
                                        print('heeeeeey **********')
                                        print('20')
                                        print('raw model contain val3')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]
                                        print('swap to 1016A')
                                    elif contain_val3 :

                                        print('30')
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]
                                        print('swap to 1016B')
                                    else :
                                        print('teeeeeesssssssssssssssssssssssst')
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        return
                                        #time.sleep(5)

                                elif '1216A' in model :
                                    #ok
                                    # 1216A 1216B 1116A 1116B 1016A 1016B

                                    id_model = "942"

                                    print("elif '1216A' in model :")
                                    val1 = "1216A"
                                    val2 = "1216B"
                                    val3 = "1116A"
                                    val4 = "1116B"
                                    val5 = '1016A'
                                    val6 = '1016B'

                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()
                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()
                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()
                                    contain_val4 = df['model'].str.contains(val4, case= False, na=False).any()
                                    contain_val5 = df['model'].str.contains(val5, case= False, na=False).any()
                                    contain_val6 = df['model'].str.contains(val6, case= False, na=False).any()

                                    if contain_val1 :
                                        print('11')

                                        print('raw model contain val3')
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                        print('swap to 1216A')
                                    elif contain_val2 :
                                        #id_model = "948"
                                        print('12')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]
                                        print('swap to 1216B')

                                    elif contain_val3 :
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]

                                    elif contain_val4 :
                                        filtered_df = df[df['model'].str.contains(val4, case=False, na=False)]

                                    elif contain_val5 :
                                        filtered_df = df[df['model'].str.contains(val5, case=False, na=False)]

                                    elif contain_val6 :
                                        filtered_df = df[df['model'].str.contains(val6, case=False, na=False)]

                                    else :
                                        text_variable.set('no model is compatible with')
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return

                                elif '2016A' in model :
                                    print('ok model',model)
                                    #2016A  2016B  contrainte mefach d

                                    id_model = "948"
                                    val1 = '2016A'
                                    val2= "2016B"
                                    val3 = "1216A"

                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()
                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()
                                    
                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()

                                    

                                    if contain_val1 :
                                        print('1++++ contain_val')

                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                    elif contain_val2 :
                                        print('2++++ contain_val')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]

                                    elif contain_val3 : 
                                        print('3++++ contain_val')
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]

                                    else :
                                        print('4++++ noooo')
                                        print(df['model'])
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return


                                #traiter les cas de B :
                                elif '1016B' in model :
                                    #ok

                                    #1016B
                                    id_model = "931"
                                    val1 = "1016B"


                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()


                                    if contain_val1 :
                                        #id_model = "948"
                                        print('03')
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                        print('swap to 1016B')

                                    else :
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return

                                elif '1116B' in model :
                                    #ok
                                    #1116B 1016B

                                    print('elif "1116B" in model :')
                                    id_model = "938"

                                    val1 = "1116B"
                                    val2 = "1016B"

                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()

                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()


                                    if contain_val1 :
                                        #id_model ="938"
                                        print('100')
                                        print('raw model contain val5')
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]

                                    elif contain_val2:
                                        #id_model ='943'
                                        print('200')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]

                                    else :
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return

                                elif '1216B' in model :
                                    #ok
                                    #1216B 1116B 1016B

                                    id_model = "943"

                                    val1 ="1216B"
                                    val2 = "1116B"
                                    val3 = "1016B"


                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()

                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()

                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()

                                    if contain_val1 :
                                        print('20')
                                        #id_model = "942"
                                        print('raw model contain val3')
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]
                                        print('swap to 1216B')

                                    elif contain_val2 :
                                        print('3000')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]
                                        print('swap to 1116B')

                                    elif contain_val3 :
                                        print('06')
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]
                                        print('swap to 1016B')

                                    else :
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return

                                elif '2016B' in model :
                                    #ok
                                    #2016B

                                    print('mod==',model)
                                    #val8 = "2016B"
                                    val2 = '2016B'
                                    val3 = "1216B"
                                    id_model = "949"

                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()

                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()
                                    if contain_val2 :
                                        print('okk :)')
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]
                                        print('swap to 2016B')

                                        print('swap to 2016A')
                                    elif contain_val3 : 
                                        print('1216B !!!')
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]
                                    else :
                                        text_variable.set('no model is compatible with')
                                        #label.config(bg="#008000",fg = "red")
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return


                                #elif 'CFI-7016'  in models_list :
                                elif 'CFI-7016'  in model :
                                    print('------PROOOOO-----')
                                    val0 = 'CFI-7016B01' #955
                                    val1 = 'CFI-7016B01Y' #957
                                    val2 = 'CFI-7016B30' #958
                                    val3 = 'CFI-7016B30E' #959

                                    contain_val0 = df['model'].str.contains(val0, case= False, na=False).any()

                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()

                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()

                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()

                                    print("contain_val0",contain_val0)
                                    print("contain_val1",contain_val1)
                                    print("contain_val2",contain_val2)

                                    print("contain_val3",contain_val3)

                                    print(" df['model']")
                                    print( df['model'])
                                    if extr == "TYJ":
                                        print('(1)')
                                        id_model = "955"
                                    elif extr == "UGP":
                                        print('(2)')
                                        id_model = "958"
                                    elif extr == "PEA":
                                        print('(3)')
                                        id_model = "957"
                                    elif extr == "B74":
                                        print('(4)')
                                        id_model = "958"
                                    elif extr == "75H":
                                        print('(5)')
                                        id_model = "959"
                                    if contain_val0 :

                                        print('contain_val0',contain_val0)
                                        #id_model = "955"
                                        filtered_df = df[df['model'].str.contains(val0, case=False, na=False)]

                                    elif contain_val1:
                                        #id_model = "957"
                                        print('contain_val1',contain_val1)
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]

                                    elif contain_val2:
                                        #id_model = "958"
                                        print('contain_val2',contain_val2)
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]

                                    elif contain_val3:
                                        print('contain_val3',contain_val3)
                                        #id_model = "959"
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]

                                    else :
                                        print('flashina model pro l model abcd 1 ')
                                        #si le model sn new est pro et snold abcd swap !
                                        #faire swapper avec le id model le plus grand !
                                        print('df')
                                        print(df)
                                        liste_model = df['model'].tolist()
                                        print('liste_model : ',liste_model)
                                        """for element in liste_model:

                                            if 'B' in element:
                                                print(f"L'élément '{element}' contient B")
                                                print('B in ')



                                            elif 'A' in element :
                                                print(f"L'élément '{element}' contient A")
                                                print('A in ')
                                                print('errorr !!')
                                                print('We cant change ')
                                                print("-----------------------------------------------")

                                                text_variable.set('No replacing Standard with Digital ❌')

                                                label.config(bg="red", fg="white")
                                                entry_snnew.config(state="normal")
                                                entry_snnew.delete(0, 'end')

                                                return """

                                        # Vérifier si 'B' existe dans la liste
                                        selected_element = None
                                        b_found = False
                                        for element in liste_model:
                                            if 'B' in element:
                                                b_found = True
                                                selected_element = element
                                                print(f"L'élément '{element}' contient B")
                                                print('B in')

                                        # Si aucun 'B' n'a été trouvé, vérifier 'A'
                                        if not b_found:
                                            for element in liste_model:
                                                if 'A' in element:
                                                    selected_element = element
                                                    print(f"L'élément '{element}' contient A")
                                                    print('A in')
                                                    print('errorr !!')
                                                    print('We cant change')
                                                    print("-----------------------------------------------")
                                                    # Actions sur l'interface graphique
                                                    text_variable.set('No replacing Standard with Digital ❌')
                                                    label.config(bg="red", fg="white")
                                                    entry_snnew.config(state="normal")
                                                    entry_snnew.delete(0, 'end')
                                                    # Si vous êtes dans une fonction, arrêter l'exécution ici
                                                    return

                                        # Utiliser l'élément sauvegardé (si trouvé)
                                        if selected_element is not None:
                                            # Trouver la ligne avec le model_idmodel maximal
                                            row_max_model_id = df.loc[df['model_idmodel'].idxmax()]
                                            print("Ligne avec model_idmodel maximal :", row_max_model_id)

                                            # Utiliser l'élément sauvegardé pour val
                                            val = selected_element
                                            print('val =', val)

                                            # Filtrer le DataFrame avec val
                                            filtered_df = df[df['model'].str.contains(val, case=False, na=False)]
                                            print('\nDataFrame filtré :')
                                            print(filtered_df)
                                        else:
                                            print("Aucun élément contenant 'A' ou 'B' n'a été trouvé.")




                                        '''row_max_model_id = df.loc[df['model_idmodel'].idxmax()]
                                        print("row_max_model_id")

                                        #val = row_max_model_id['model']
                                        val = element
                                        print('val = ',val)
                                        filtered_df = df[df['model'].str.contains(val, case=False, na=False)]
                                        print('filtered_df')
                                        print(filtered_df)
                                        quit()'''

                                else :

                                    text_variable.set('check the snnew !')
                                    print('check the snnew !')
                                    label.config(bg="red",fg = "white")
                                    entry_snnew.config(state= "normal")
                                    entry_snnew.delete(0,'end')
                                print('****filtered_df********',filtered_df)
                                print('idmode**=)',id_model)

                            else :

                                print('****filtered_df not empty****')
                                print('filtered_df before')
                                print(filtered_df)

                            print("filtered_df")
                            label.config(bg="#008000",fg = "white")
                            text_variable.set('print .. ')
                            print('filtered_df')
                            print(filtered_df)



                            print('first sn that have same model = ')
                            print(filtered_df['snold'].iloc[0])
                            snOld = filtered_df['snold'].iloc[0]
                            print('-------------filtered_df---------------')
                            print(filtered_df)
                            print('-------------snOld1---------------')
                            print(snOld)

                            peripherique = filtered_df['peripherals'].values[0]
                            print("peripherique",peripherique)
                            #quit()
                            #update_snnew_data
                            rma = get_rma(snOld)
                            #?
                            print('rma : ',rma)
                            print('rma[0] : ',rma[0])
                            rma = rma[0]
                            #CycleonId = "test_swap_app"
                            print('modification base de données table sn sn mvt ')
                            print('snOld = ',snOld)
                            print('snnew = ',snnew)
                            print('rma = ',rma)


                            today = date.today()
                            today_value = today.strftime("%d/%m/%Y")

                            #script_dir = os.path.dirname(os.path.abspath(__file__))
                            script_dir = "D:\SWAP"
                            lang = get_lang_fromSNNEW(snOld)
                            print('******lang***********')
                            print(lang)
                            if lang == "Spain":
                                print('1*')
                                print('**Spain')
                                html_path = os.path.join(script_dir, "data", "temps_Spain.html")
                                entry_snnew.config(state='disabled')
                                #entry_snnew.delete(0,'end')
                                entry_snnew.delete(0,'end')

                            elif lang == "Portugal":
                                print('2*')
                                print('**Portugal')
                                html_path = os.path.join(script_dir, "data", "temps_Portugal.html")
                                entry_snnew.config(state='disabled')


                                print('*******Portugal*********')



                            else :
                                print('3*')
                                print('1 - nol lang !! ')
                                label.config(bg="#FF0800",fg = "white")
                                text_variable.set('Missing information for language!')

                                entry_snnew.config(state= "normal")
                                entry_snnew.delete(0,'end')

                                raise ValueError('Missing information for language!')


                            #html_path = os.path.join(script_dir, "data", "temps.html")
                            convert_to_pdf(html_path,model,snnew,rma,today_value,snOld)

                            #print('outbound_label: --- ',outbound_label)
                            if outbound_label is None :
                                print('if outbound_label is None : ')
                                text_variable.set('No outbound_label !')

                                label.config(bg="red",fg = "white")
                                entry_snnew.config(state= "normal")
                                entry_snnew.delete(0,'end')
                                raise ValueError('1- No :) outbound_label!')
                            else :
                                print('okkkkkkkkkkk')

                            name_var_cyclon = StringVar()
                            name_var_cyclon.set('')

                            print('model_counts**',model_counts)
                            print('model_counts mesure 1 ',len(model_counts))
                            label_cyclonid.grid(row=len(model_counts) + 40, column=0, pady=5)
                            text_variable.set('Give cyclonid ')

                            try:
                                print('its ok')
                                entry_cylonid.grid(row=len(model_counts) + 55, column=0, pady=5)
                            except Exception as e:
                                # Handle the exception here
                                print("An error occurred:", e)

                            # Set focus on entry_cylonid
                            entry_cylonid.focus_set()

                            entry_cylonid.bind("<Return>", get_cyclonid)
                            #print("time.sleep(10)")
                            #time.sleep(10)

                else :

                    print('1else decision !!!!!!!!!!!! ')
                    print(snold_nxtjob)
                    entry_snnew.config(state= "normal")
                    entry_snnew.delete(0,'end')

                    label.config(bg="red",fg = "white")
                    text_variable.set(snold_nxtjob)

                    #quit()

            elif any(valeur_pro in extr for valeur_pro in valeurs_permises_pro):
                print('heey proooo!!!!!!!!!!!!!',snnew)
                snold_nxtjob = verif_nextjob_mvt_snold_de_snnew(snnew)
                print('snold_nxtjob',snold_nxtjob)
                if snold_nxtjob == True :
                    print('trueeeeeeeeeee')

                    print("1*La valeur est valide.")
                    print('*****df_product_code_pro***')
                    print(df_product_code_pro)
                    for index, row in df_product_code_pro.iterrows():
                        print("---------2---------")
                        #if any(snnew.startswith(value) for value in df_product_code['SCEIProductCode']):
                        value = row['SCEIProductCode']

                        print('value_pro',value)

                        #if (value in snnew for value in df_product_code['SCEIProductCode']):
                        if (value in snnew) :
                            print('00_contain_pro')
                            ModelNumber = row['ModelNumber']
                            print('ModelNumber de snnew2 = ',ModelNumber)
                            ModelNumber = ModelNumber[:9]
                            print('++',ModelNumber)


                            id_model = get_id_model(ModelNumber)

                            print('*id_model : ',id_model)

                            print('id_model[0] : ',id_model[0])

                            if id_model :
                                id_model = id_model[0]
                                print('1',id_model)
                            else :
                                id_model = id_model[0]
                                print('----------------------------')
                                print('idmodel vide :) ')
                                print('2',id_model)

                            print('**id_model[0]  : ',id_model)

                            print(f"La valeur {snnew} contient {row['SCEIProductCode']} a été trouvée à l'index {index} with Chassis = {row['Chassis']} with model = {row['ModelNumber']}")
                            print('')
                            #quit()
                            chassis = row['Chassis']
                            model = row['ModelNumber']

                            print('chassis : ',chassis)
                            print('model : ',model)

                            df['model'] = df['model'].str.strip()

                            print('the first dataframe df pro ')
                            print(df)
                            print('***end df***')
                            print('values that have same model :')
                            models_list = df['model']
                            models_list = models_list.str.replace(' ', '')

                            models_list = models_list.to_string(index=False)

                            print('models_list--',models_list)

                            print('model',model)
                            filtered_df = df.loc[df['model'] == model]
                            print('++filtered_df++')
                            print(filtered_df)
                            print('++1++')

                            if filtered_df.empty:
                                print('no model :)2222')
                                '''if 'B' in model :
                                    print('222222222222222222222222222')
                                    print('B in model !!!')
                                    print(df['model'])
                                    found_A = None
                                    found_B = None


                                    for index, row in df.iterrows():
                                        model_value = row['model']
                                        print("row['model']",model_value)

                                        if 'A' in row['model']:
                                            found_A = row
                                            print('1A found!')

                                            print('row')
                                            print(row)
                                            print('A innn ! ')
                                            break

                                        if 'B' in row['model']:
                                            found_B = row
                                            print('B found!')

                                            print('B1 innn ! ')
                                            print('no swap ')

                                    print('***********')
                                    print('found_A')
                                    print(found_A)
                                    print("found_B")
                                    print(found_B)

                                    if found_A is not None and not found_A.empty:
                                        # Faire le traitement avec A
                                        print('Using A from row:', found_A)
                                        # Ajoute ici ton traitement pour A
                                        print('We cant change ')
                                        print("-----------------------------------------------")

                                        text_variable.set('2 No replacing Standard with Digital ❌')
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return

                                    elif found_B is not None and not found_B.empty:
                                        # Faire le traitement avec B
                                        print('Using B from row:', found_B)
                                        # Ajoute ici ton traitement pour B
                                        # ...
                                        text_variable.set('Used B ✅')
                                        label.config(bg="orange", fg="white")


                                    for index, row in df.iterrows():
                                        if 'A' in row['model']:
                                            print('row')
                                            print(row)
                                            print('A innn ! ')

                                        if 'B' in row['model']:

                                            print('B2 innn ! ')
                                            print('no swap ')

                                        print('We cant change ')
                                        print("-----------------------------------------------")

                                        text_variable.set('No replacing Standard with Digital ❌')
                                        label.config(bg="red",fg = "white")
                                        entry_snnew.config(state= "normal")
                                        entry_snnew.delete(0,'end')
                                        return '''


                                if 'CFI-7016'  in model :

                                    val0 = 'CFI-7016B01' #955
                                    val1 = 'CFI-7016B01Y' #957
                                    val2 = 'CFI-7016B30' #958
                                    val3 = 'CFI-7016B30E' #959

                                    contain_val0 = df['model'].str.contains(val0, case= False, na=False).any()


                                    contain_val1 = df['model'].str.contains(val1, case= False, na=False).any()


                                    contain_val2 = df['model'].str.contains(val2, case= False, na=False).any()

                                    contain_val3 = df['model'].str.contains(val3, case= False, na=False).any()

                                    print("contain_val0",contain_val0)
                                    print("contain_val1",contain_val1)
                                    print("contain_val2",contain_val2)

                                    print("contain_val3",contain_val3)

                                    print(" df['model']")
                                    print( df['model'])
                                    if extr == "TYJ":
                                        id_model = "955"
                                    elif extr == "UGP":
                                        id_model = "958"
                                    elif extr == "PEA":
                                        id_model = "957"
                                    elif extr == "B74":
                                        id_model = "958"
                                    elif extr == "75H":
                                        id_model = "959"
                                    if contain_val0 :

                                        print('cntain1')
                                        #id_model = "955"
                                        filtered_df = df[df['model'].str.contains(val0, case=False, na=False)]

                                    elif contain_val1:
                                        #id_model = "957"
                                        filtered_df = df[df['model'].str.contains(val1, case=False, na=False)]

                                    elif contain_val2:
                                        #id_model = "958"
                                        filtered_df = df[df['model'].str.contains(val2, case=False, na=False)]

                                    elif contain_val3:
                                        #id_model = "959"
                                        filtered_df = df[df['model'].str.contains(val3, case=False, na=False)]

                                    else :
                                        print('flashina model pro l model abcd 2 ')

                                        #valeurs_permises_pro = ['TYJ','UGP','PEA','B74','75H']

                                        val_2016A = '2016A'
                                        val_2016B = '2016B'

                                        val_1216A = "1216A"
                                        val_1216B = "1216B"

                                        val_1116B = "1116B"
                                        val_1116A = "1116A"

                                        val_1016A = "1016A"
                                        val_1016B = "1016B"

                                        #affect id_model selon product code console
                                        #2016A
                                        contain_val_2016A = df['model'].str.contains(val_2016A, case= False, na=False).any()
                                        contain_val_2016B = df['model'].str.contains(val_2016B, case= False, na=False).any()

                                        contain_val_1216A = df['model'].str.contains(val_1216A, case= False, na=False).any()
                                        contain_val_1216B = df['model'].str.contains(val_1216B, case= False, na=False).any()

                                        contain_val_1116B = df['model'].str.contains(val_1116B, case= False, na=False).any()
                                        contain_val_1116A = df['model'].str.contains(val_1116A, case= False, na=False).any()

                                        contain_val_1016A = df['model'].str.contains(val_1016A, case= False, na=False).any()
                                        contain_val_1016B = df['model'].str.contains(val_1016B, case= False, na=False).any()


                                        if contain_val_2016A  :
                                            filtered_df = df[df['model'].str.contains(val_2016A, case=False, na=False)]

                                        #2016B

                                        elif contain_val_2016B :
                                            filtered_df = df[df['model'].str.contains(val_2016B, case=False, na=False)]

                                        #1216A

                                        elif contain_val_1216A :
                                            filtered_df = df[df['model'].str.contains(val_1216A, case=False, na=False)]

                                        #1216B

                                        elif contain_val_1216B :
                                            filtered_df = df[df['model'].str.contains(val_1216B, case=False, na=False)]

                                        elif contain_val_1116B:
                                            filtered_df = df[df['model'].str.contains(val_1116B, case=False, na=False)]

                                        elif contain_val_1116A :
                                            filtered_df = df[df['model'].str.contains(val_1116A, case=False, na=False)]

                                        elif contain_val_1016A :
                                            filtered_df = df[df['model'].str.contains(val_1016A, case=False, na=False)]

                                        elif contain_val_1016B :
                                            filtered_df = df[df['model'].str.contains(val_1016B, case=False, na=False)]

                                        else :
                                            #affichage msg erreur pas de model a swapper
                                            label.config(bg="red",fg = "white")
                                            entry_snnew.config(state= "normal")
                                            entry_snnew.delete(0,'end')
                                            text_variable.set('There is no Model to swap ')


                                    print('filtered_df selon lexistant ')

                                    print('traitement de model pro')


                                else :
                                    print('flashina model pro l model abcd 3')
                                    #affecter model pro si
                                    '''print('thats not pro model !!')
                                    label.config(bg="red",fg = "white")
                                    entry_snnew.config(state= "normal")
                                    entry_snnew.delete(0,'end')
                                    text_variable.set('There is no Model Pro in stock to swap ')'''

                                    print('flashina model pro l model abcd 1 ')
                                    # si le model sn new est pro et snold abcd swap !
                                    # faire swapper avec le id model le plus grand !
                                    print('df')
                                    print(df)
                                    liste_model = df['model'].tolist()
                                    print('liste_model : ', liste_model)
                                    """for element in liste_model:

                                        if 'B' in element:
                                            print(f"L'élément '{element}' contient B")
                                            print('B in ')



                                        elif 'A' in element :
                                            print(f"L'élément '{element}' contient A")
                                            print('A in ')
                                            print('errorr !!')
                                            print('We cant change ')
                                            print("-----------------------------------------------")

                                            text_variable.set('No replacing Standard with Digital ❌')

                                            label.config(bg="red", fg="white")
                                            entry_snnew.config(state="normal")
                                            entry_snnew.delete(0, 'end')

                                            return """

                                    # Vérifier si 'B' existe dans la liste
                                    selected_element = None
                                    b_found = False
                                    for element in liste_model:
                                        if 'B' in element:
                                            b_found = True
                                            selected_element = element
                                            print(f"L'élément '{element}' contient B")
                                            print('B in')

                                    # Si aucun 'B' n'a été trouvé, vérifier 'A'
                                    if not b_found:
                                        for element in liste_model:
                                            if 'A' in element:
                                                selected_element = element
                                                print(f"L'élément '{element}' contient A")
                                                print('A in')
                                                print('errorr !!')
                                                print('We cant change')
                                                print("-----------------------------------------------")
                                                # Actions sur l'interface graphique
                                                text_variable.set('No replacing Standard with Digital ❌')
                                                label.config(bg="red", fg="white")
                                                entry_snnew.config(state="normal")
                                                entry_snnew.delete(0, 'end')
                                                # Si vous êtes dans une fonction, arrêter l'exécution ici
                                                return

                                    # Utiliser l'élément sauvegardé (si trouvé)
                                    if selected_element is not None:
                                        # Trouver la ligne avec le model_idmodel maximal
                                        row_max_model_id = df.loc[df['model_idmodel'].idxmax()]
                                        print("Ligne avec model_idmodel maximal :", row_max_model_id)

                                        # Utiliser l'élément sauvegardé pour val
                                        val = selected_element
                                        print('val =', val)

                                        # Filtrer le DataFrame avec val
                                        filtered_df = df[df['model'].str.contains(val, case=False, na=False)]
                                        print('\nDataFrame filtré :')
                                        print(filtered_df)
                                    else:
                                        print("Aucun élément contenant 'A' ou 'B' n'a été trouvé.")



                            else :

                                print('****filtered_df not empty****')
                                print('filtered_df before')
                            print(filtered_df)
                            print("filtered_df")
                            label.config(bg="#008000",fg = "white")
                            text_variable.set('print .. ')
                            print(filtered_df)

                            print('first sn that have same model = ')
                            print(filtered_df['snold'].iloc[0])
                            snOld = filtered_df['snold'].iloc[0]
                            print('-------------filtered_df---------------')
                            print(filtered_df)
                            print('-------------snOld2---------------')
                            print(snOld)
                            peripherique = filtered_df['peripherals'].values[0]
                            print("peripherique",peripherique)
                            #quit()
                            #update_snnew_data
                            rma = get_rma(snOld)
                            #?
                            print('rma : ',rma)
                            print('rma[0] : ',rma[0])
                            rma = rma[0]
                            #CycleonId = "test_swap_app"
                            print('modification base de données table sn sn mvt ')
                            print('snOld = ',snOld)
                            print('snnew = ',snnew)
                            print('rma = ',rma)
                            #print('CycleonId = ',CycleonId)

                            today = date.today()
                            today_value = today.strftime("%d/%m/%Y")

                            #script_dir = os.path.dirname(os.path.abspath(__file__))
                            script_dir = "D:\SWAP"

                            #convert pdf
                            lang = get_lang_fromSNNEW(snOld)
                            if lang == "Spain":
                                print('***Spain')
                                html_path = os.path.join(script_dir, "data", "temps_Spain.html")
                                entry_snnew.config(state='disabled')
                                entry_snnew.delete(0,'end')



                            elif lang == "Portugal":
                                print('***Portugal')
                                html_path = os.path.join(script_dir, "data", "temps_Portugal.html")
                                entry_snnew.config(state='disabled')
                                entry_snnew.delete(0,'end')


                            else :
                                print('2 nol lang !! ')
                                time.sleep(5)
                                label.config(bg="red",fg = "white")
                                text_variable.set('Missing information for language!')
                                #label.after(5000, window.quit)

                                entry_snnew.config(state= "normal")
                                entry_snnew.delete(0,'end')

                            convert_to_pdf(html_path,model,snnew,rma,today_value,snOld)

                            if outbound_label is None :

                                time.sleep(5)
                                label.config(bg="red",fg = "white")
                                #time.sleep(5)
                                text_variable.set('No outbound_label !')
                                print('nokkkkkkkkkkkkk2')
                                #entry_snnew.delete('')
                                entry_snnew.config(state= "normal")
                                entry_snnew.delete(0,'end')
                                raise ValueError('2- No :) outbound_label!')

                            else :
                                print('okkkkkkkkkkk')

                            name_var_cyclon = StringVar()
                            name_var_cyclon.set('')

                            print('model_counts**',model_counts)
                            print('model_counts mesure 2',len(model_counts))
                            label_cyclonid.grid(row=len(model_counts) + 40, column=0, pady=5)
                            text_variable.set('Give cyclonid ')

                            try:
                                entry_cylonid.grid(row=len(model_counts) + 55, column=0, pady=5)
                            except Exception as e:
                                # Handle the exception here
                                print("An error occurred:", e)

                            # Set focus on entry_cylonid
                            entry_cylonid.focus_set()

                            entry_cylonid.bind("<Return>", get_cyclonid)

                else :
                    print('2else decision !!!!!!!!!!!! ')
                    print(snold_nxtjob)
                    entry_snnew.config(state= "normal")
                    entry_snnew.delete(0,'end')

                    label.config(bg="red",fg = "white")
                    text_variable.set(snold_nxtjob)

            else:
                print('nooooooo :)')
                label.config(bg="red",fg = "white")
                entry_snnew.config(state= "normal")
                entry_snnew.delete(0,'end')
                text_variable.set('The SNNEW is not valid.')
                print("***La valeur n'est pas valide.")

    else :

        print('-----------------elseee-----------------------------------')
        label.config(bg="#FF0800",fg = "white")
        print('noo')
        entry_snnew.config(state= "normal")
        entry_snnew.delete(0,'end')
        text_variable.set("Check  SNNEW Value ! ")

cyclonid = None
snOld = None
rma = None 

def get_id_model(model_name):
    print('get_id_model')
    conn = mysql.connector.connect(
        host=HostIp, 
        database=DataBaseName, 
        port=port1, 
        user=User, 
        password=Password,
        use_pure=True  # Force using the pure Python implementation
    )
    cursor = conn.cursor()

    try:
        query = 'select * from model where model like %s'
        print("model_name from get_id_model ",model_name)

        cursor.execute(query, ('%' + model_name + '%',))

        #data = cursor.fetchone()
        data = cursor.fetchall()
        print('--data--',data)

        return data
    finally:
        # Close the database connection in a finally block to ensure it happens even if an exception occurs
        cursor.close()
        conn.close()


def verif_cyclonid(snOld, cyclonid):
    try:
        conn = mysql.connector.connect(host=HostIp, database=DataBaseName, port=port1, user=User, password=Password)
        cursor = conn.cursor()
        sql = f"SELECT OutboundCycleonID FROM sn WHERE snold = '{snOld}' ORDER BY idsn DESC LIMIT 1 ;"
        cursor.execute(sql)
        result = cursor.fetchone()

        if result:
            CycleonId = result[0]
            print('CycleonId_1', CycleonId)

            if CycleonId is not None:
                CycleonId = CycleonId.replace(" ", "").replace("(", "").replace(")", "")
            else: 
                label.config(bg="red", fg="white")
                text_variable.set('Verify CycleonId in database is None!')
                print('CycleonId is none')
            
            print('CycleonId_2', CycleonId)
        else:
            CycleonId = None  # Empty CycleonId if no result found

    finally:
        cursor.close()
        conn.close()
    
    return CycleonId


def get_cyclonid(e):

    global  snOld,snnew,rma,cyclonid,id_model,peripherique
    cyclonid = entry_cylonid.get()
    print('***get_cyclonid*****')
    #entry_snnew.delete(0,'end')
    entry_snnew.config(state='disabled')
    entry_cylonid.delete(0,END)
    print("cyclonid",cyclonid)
    if cyclonid : 
        print('-------id_model---------- : ',id_model)

        #quit()
        print('entry_cylonid not empty :) ')
        print(cyclonid)
        #if cyclonid is the same in the database 

        cyclon_verif = verif_cyclonid(snOld,cyclonid)
        print('++cyclon_verif : ',cyclon_verif)
        #entry_cylonid.grid_forget()
        if cyclon_verif is None:
            label.config(bg="red",fg = "white")
            text_variable.set(' Verify CycleonId in database is None ! ')
            entry_cylonid.grid_forget()
            label_cyclonid.grid_forget()
            #make entry snnew shows normal 
            entry_snnew.config(state= "normal")
            entry_snnew.delete(0,'end')

        else : 

            if cyclon_verif == cyclonid :
                
                update_snnew_data(snOld,snnew,rma,cyclonid,id_model)

                entry_snnew.config(state= "normal")
                entry_snnew.delete(0,'end')

                label_cyclonid.grid_forget()
                entry_cylonid.grid_forget()
                
                #text_variable.set('sn with peripherals : ' + peripherique)
                if peripherique :
                    text_variable.set('sn with peripherals : ' + peripherique)
                    label.config(bg="#FF4500",fg = "black")
                else :
                     text_variable.set('sn with no peripherals')
                     label.config(bg="yellow",fg = "black")

            else :
                label.config(bg="red",fg = "white")
                #text_variable.set(' the value in database is not the same value entred !')
                text_variable.set(' Check cyclonid value for snold :'+ snOld)
    else :
        label.config(bg="red",fg = "white")
        text_variable.set('cyclonid is empty')
        print('entry_cylonid is empty :( ')




def center_text(event):
    label_width = label.winfo_width()
    window_width = window.winfo_width()
    relx = (window_width - label_width) / 2 / window_width
    label.place_configure(relx=relx)




text_variable = StringVar()
text_variable.set("give SN  ! ")

label = Label(window, textvariable=text_variable, font=('Helvetica 16 bold'))
label.config(bg="#008000",fg = "white")
label.place(relx=0.0, rely=1, anchor='sw', width=2000, height=100)
window.bind("<Configure>", center_text)

# Initial creation of entry_snnew
name_var = StringVar()
name_var.set('')
entry_snnew = Entry(window, textvariable=name_var,width=20, font=('calibre', 12, 'bold'),foreground = 'green') 

entry_cylonid = Entry(window,width=20, font=('calibre', 12, 'bold'),foreground = 'green')
label_cyclonid = Label(window, text="Give cyclonid to swap :", font="Arial 14 bold")



# Initial call to create_labels
create_labels('model', 'quantity')

window.mainloop()
