@echo off
echo ========================================
echo Création du package SWAP v1.5.4
echo ========================================
echo.

REM Créer le dossier de package
set PACKAGE_DIR=SWAP_v1.5.4_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

REM Copier l'exécutable principal
echo Copie de l'exécutable principal...
copy "dist\SWAP_v1.5.4_IMPRESSION_MANUELLE.exe" "%PACKAGE_DIR%\"

REM Copier les fichiers de données nécessaires
echo Copie des fichiers de données...
xcopy "data" "%PACKAGE_DIR%\data" /E /I /Y
copy ".env" "%PACKAGE_DIR%\" 2>nul

REM Créer le script de lancement
echo Création du script de lancement...
echo @echo off > "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo SWAP v1.5.4 - Impression Manuelle >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo SOLUTION GHOSTSCRIPT: >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo - Plus d'interface Ghostscript >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo - Impression manuelle avec bouton >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo - PDF s'ouvre pour visualisation >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo echo Démarrage de l'application... >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo SWAP_v1.5.4_IMPRESSION_MANUELLE.exe >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo if errorlevel 1 ( >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo     echo ERREUR: Impossible de démarrer l'application >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo     pause >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"
echo ^) >> "%PACKAGE_DIR%\run_SWAP_v1.5.4.bat"

REM Créer le script de vérification
echo Création du script de vérification...
echo @echo off > "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo SWAP v1.5.4 - Vérification du système >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo Vérification des imprimantes disponibles... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo echo Vérification terminée. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"
echo pause >> "%PACKAGE_DIR%\VERIFICATION_v1.5.4.bat"

REM Créer le fichier d'information
echo Création du fichier d'information...
echo SWAP Version 1.5.4 - Impression Manuelle > "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ======================================== >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo Date de création: %date% %time% >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo PROBLÈME GHOSTSCRIPT RÉSOLU: >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ============================ >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Plus d'interface Ghostscript qui s'ouvre >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Impression automatique désactivée >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ PDF s'ouvre pour visualisation uniquement >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Bouton d'impression manuelle ajouté >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Contrôle total de l'impression par l'utilisateur >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo UTILISATION: >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ============ >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 1. Scanner un SN >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 2. Le PDF s'ouvre automatiquement pour visualisation >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 3. Cliquer sur le bouton "📄 Imprimer dernier PDF" pour imprimer >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 4. Ou utiliser Ctrl+P dans le PDF ouvert >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo CORRECTIONS MAINTENUES: >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ======================= >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Logique intelligente de sélection d'imprimante >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Priorité 1: HP LaserJet M109-M112 >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Priorité 2: EPSON ET contenant 2810 >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ Priorité 3: Imprimante par défaut (si non-POSTEK) >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ✓ POSTEK réservé uniquement aux étiquettes outbound >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo ============= >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 1. Copier tout le dossier SWAP_v1.5.4_Package sur le PC cible >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 2. Exécuter VERIFICATION_v1.5.4.bat pour vérifier les imprimantes >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.5.4.bat >> "%PACKAGE_DIR%\VERSION_1.5.4_INFO.txt"

REM Créer l'archive ZIP
echo Création de l'archive ZIP...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath 'SWAP_v1.5.4_IMPRESSION_MANUELLE.zip' -Force"

echo.
echo ========================================
echo ✓ PACKAGE CRÉÉ AVEC SUCCÈS !
echo ========================================
echo.
echo Dossier: %PACKAGE_DIR%
echo Archive: SWAP_v1.5.4_IMPRESSION_MANUELLE.zip
echo.
echo PROBLÈME GHOSTSCRIPT RÉSOLU:
echo - Plus d'interface Ghostscript
echo - Impression manuelle avec bouton
echo - PDF s'ouvre pour visualisation
echo - Contrôle total par l'utilisateur
echo.
echo Le package est prêt pour le déploiement !
echo.
