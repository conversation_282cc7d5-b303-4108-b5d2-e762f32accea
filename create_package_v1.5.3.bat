@echo off
echo ========================================
echo Création du package SWAP v1.5.3
echo ========================================
echo.

REM Créer le dossier de package
set PACKAGE_DIR=SWAP_v1.5.3_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

REM Copier l'exécutable principal
echo Copie de l'exécutable principal...
copy "dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe" "%PACKAGE_DIR%\"

REM Copier les fichiers de données nécessaires (SANS SumatraPDF corrompu)
echo Copie des fichiers de données...
xcopy "data" "%PACKAGE_DIR%\data" /E /I /Y
copy ".env" "%PACKAGE_DIR%\" 2>nul

REM NE PAS copier SumatraPDF corrompu
echo ATTENTION: SumatraPDF corrompu non inclus (impression native Windows)

REM Créer le script de lancement
echo Création du script de lancement...
echo @echo off > "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo echo SWAP v1.5.3 - Impression Native Windows >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo echo. >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo echo Démarrage de l'application... >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo if errorlevel 1 ( >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo     echo ERREUR: Impossible de démarrer l'application >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo     pause >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"
echo ^) >> "%PACKAGE_DIR%\run_SWAP_v1.5.3.bat"

REM Créer le script de vérification
echo Création du script de vérification...
echo @echo off > "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo SWAP v1.5.3 - Vérification du système >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo ======================================== >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo Vérification des imprimantes disponibles... >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo echo Vérification terminée. >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"
echo pause >> "%PACKAGE_DIR%\VERIFICATION_v1.5.3.bat"

REM Créer le fichier d'information
echo Création du fichier d'information...
echo SWAP Version 1.5.3 - Impression Native Windows > "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ======================================== >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo Date de création: %date% %time% >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo PROBLÈME RÉSOLU: >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ================ >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Plus d'interface Ghostscript qui s'ouvre >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ SumatraPDF corrompu évité >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Impression native Windows >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Méthodes d'impression multiples avec fallback >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Impression 100%% silencieuse >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo CORRECTIONS MAINTENUES: >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ======================= >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Logique intelligente de sélection d'imprimante >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Priorité 1: HP LaserJet M109-M112 >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Priorité 2: EPSON ET contenant 2810 >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ Priorité 3: Imprimante par défaut (si non-POSTEK) >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ✓ POSTEK réservé uniquement aux étiquettes outbound >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo MÉTHODES D'IMPRESSION: >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ===================== >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 1. os.startfile(pdf, "print") - Méthode principale >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 2. PowerShell Start-Process - Fallback 1 >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 3. rundll32 shell32.dll - Fallback 2 >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo. >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo ============= >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 1. Copier tout le dossier SWAP_v1.5.3_Package sur le PC cible >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 2. Exécuter VERIFICATION_v1.5.3.bat pour vérifier les imprimantes >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.5.3.bat >> "%PACKAGE_DIR%\VERSION_1.5.3_INFO.txt"

REM Créer l'archive ZIP
echo Création de l'archive ZIP...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath 'SWAP_v1.5.3_NATIVE_WINDOWS.zip' -Force"

echo.
echo ========================================
echo ✓ PACKAGE CRÉÉ AVEC SUCCÈS !
echo ========================================
echo.
echo Dossier: %PACKAGE_DIR%
echo Archive: SWAP_v1.5.3_NATIVE_WINDOWS.zip
echo.
echo PROBLÈME RÉSOLU: 
echo - Plus d'interface Ghostscript
echo - SumatraPDF corrompu évité
echo - Impression native Windows
echo.
echo Le package est prêt pour le déploiement !
echo.
