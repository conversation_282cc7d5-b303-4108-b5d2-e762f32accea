@echo off
echo ========================================
echo SWAP Version 1.5.0 - Build Script
echo Correction d'impression intelligente
echo ========================================

echo.
echo Verification de l'environnement...

:: Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    pause
    exit /b 1
)

echo Python detecte: 
python --version

echo.
echo Verification des dependances...

:: Installer les dependances si necessaire
echo Installation des dependances...
pip install -r requirements.txt

echo.
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe" del "dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe"

echo.
echo Creation de l'executable avec PyInstaller...
pyinstaller SWAP_v1.5.0.spec

echo.
echo Verification de la creation...
if exist "dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe" (
    echo ✓ Executable cree avec succes!
    echo Emplacement: dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe
    
    echo.
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe" | find "SWAP_v1.5.0"
    
) else (
    echo ✗ ERREUR: L'executable n'a pas ete cree
    echo Verifiez les erreurs ci-dessus
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build termine avec succes!
echo ========================================
echo.
echo L'executable est disponible dans: dist\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe
echo.
pause
