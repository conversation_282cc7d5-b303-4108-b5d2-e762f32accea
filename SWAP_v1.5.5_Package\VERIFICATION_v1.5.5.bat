@echo off 
echo ======================================== 
echo SWAP v1.5.5 - Vérification du système 
echo ======================================== 
echo. 
echo Vérification des imprimantes disponibles... 
python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" 
echo. 
echo Vérification Adobe Reader... 
if exist "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe" echo ✓ Adobe Acrobat DC trouvé 
if exist "C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" echo ✓ Adobe Reader DC (x86) trouvé 
if exist "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" echo ✓ Adobe Reader DC trouvé 
echo. 
echo Vérification terminée. 
pause 
