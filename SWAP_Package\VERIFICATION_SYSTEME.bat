@echo off
echo ===============================================
echo     VERIFICATION DU SYSTEME POUR SWAP
echo ===============================================
echo.

echo Verification de Windows...
ver
echo.

echo Verification des imprimantes installees...
echo.
wmic printer get name,status | findstr /v "Status"
echo.

echo Recherche des imprimantes specifiques...
echo.

wmic printer get name | findstr /i "HP LaserJet M109" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] HP LaserJet M109-M112 detectee
) else (
    echo [!] HP LaserJet M109-M112 NON detectee
)

wmic printer get name | findstr /i "EPSON ET" | findstr "2810" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] EPSON ET 2810 detectee
) else (
    echo [!] EPSON ET 2810 NON detectee
)

wmic printer get name | findstr /i "POSTEK" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] POSTEK detectee
) else (
    echo [!] POSTEK NON detectee
)

echo.
echo Verification des fichiers SWAP...
if exist "SWAP_v1.4.3.exe" (
    echo [OK] SWAP_v1.4.3.exe present
) else (
    echo [ERREUR] SWAP_v1.4.3.exe MANQUANT
)

if exist "data" (
    echo [OK] Dossier data present
) else (
    echo [ERREUR] Dossier data MANQUANT
)

if exist ".env" (
    echo [OK] Fichier .env present
) else (
    echo [!] Fichier .env NON present - vous devez le creer
)

echo.
echo Verification de la connectivite reseau...
ping -n 1 8.8.8.8 >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] Connexion Internet disponible
) else (
    echo [!] Probleme de connexion Internet
)

echo.
echo ===============================================
echo     VERIFICATION TERMINEE
echo ===============================================
echo.
echo Si tous les elements sont [OK], vous pouvez lancer SWAP.
echo Sinon, consultez le README_INSTALLATION.txt
echo.
pause
