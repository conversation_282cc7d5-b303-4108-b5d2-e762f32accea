@echo off
echo ========================================
echo Téléchargement des outils PDF 64-bit
echo ========================================
echo.

REM Créer le dossier data s'il n'existe pas
if not exist "data" mkdir "data"

echo Téléchargement d'outils PDF compatibles 64-bit...
echo.

REM 1. Télécharger SumatraPDF 64-bit (priorité 1)
echo [1/3] Téléchargement de SumatraPDF 64-bit...
curl -L -o "data\SumatraPDF-64bit.zip" "https://files.sumatrapdfreader.org/file/kjk-files/software/sumatrapdf/rel/SumatraPDF-3.4.6-64.zip"

if exist "data\SumatraPDF-64bit.zip" (
    echo ✓ SumatraPDF 64-bit téléchargé
    echo Extraction...
    powershell -command "Expand-Archive -Path 'data\SumatraPDF-64bit.zip' -DestinationPath 'data\' -Force"
    
    if exist "data\SumatraPDF.exe" (
        echo ✓ SumatraPDF 64-bit extrait avec succès
        del "data\SumatraPDF-64bit.zip"
    ) else (
        echo ⚠ Extraction SumatraPDF échouée
    )
) else (
    echo ⚠ Téléchargement SumatraPDF échoué
)

echo.
echo [2/3] Téléchargement de PDFtk Server (alternative à PDFtoPrinter)...
REM PDFtk Server est compatible 64-bit et peut imprimer des PDF
curl -L -o "data\pdftk-server.exe" "https://www.pdflabs.com/tools/pdftk-the-pdf-toolkit/pdftk_server-2.02-win-setup.exe"

if exist "data\pdftk-server.exe" (
    echo ✓ PDFtk Server téléchargé
) else (
    echo ⚠ Téléchargement PDFtk échoué
)

echo.
echo [3/3] Création d'un script PowerShell d'impression...
REM Créer un script PowerShell pour impression directe
echo # Script PowerShell pour impression PDF > "data\print-pdf.ps1"
echo param^( >> "data\print-pdf.ps1"
echo     [string]$PdfPath, >> "data\print-pdf.ps1"
echo     [string]$PrinterName >> "data\print-pdf.ps1"
echo ^) >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo try { >> "data\print-pdf.ps1"
echo     # Méthode 1: Utiliser Adobe Reader si disponible >> "data\print-pdf.ps1"
echo     $adobePaths = @^( >> "data\print-pdf.ps1"
echo         "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe", >> "data\print-pdf.ps1"
echo         "C:\Program Files ^(x86^)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe", >> "data\print-pdf.ps1"
echo         "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" >> "data\print-pdf.ps1"
echo     ^) >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo     $adobeFound = $false >> "data\print-pdf.ps1"
echo     foreach ^($path in $adobePaths^) { >> "data\print-pdf.ps1"
echo         if ^(Test-Path $path^) { >> "data\print-pdf.ps1"
echo             Write-Host "Impression avec Adobe: $path" >> "data\print-pdf.ps1"
echo             Start-Process -FilePath $path -ArgumentList "/t", $PdfPath, $PrinterName -Wait -WindowStyle Hidden >> "data\print-pdf.ps1"
echo             $adobeFound = $true >> "data\print-pdf.ps1"
echo             break >> "data\print-pdf.ps1"
echo         } >> "data\print-pdf.ps1"
echo     } >> "data\print-pdf.ps1"
echo. >> "data\print-pdf.ps1"
echo     if ^(-not $adobeFound^) { >> "data\print-pdf.ps1"
echo         # Méthode 2: Utiliser l'impression Windows native >> "data\print-pdf.ps1"
echo         Write-Host "Adobe non trouvé, utilisation impression Windows" >> "data\print-pdf.ps1"
echo         $printJob = Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "copy", "`"$PdfPath`"", "`"\\localhost\$PrinterName`"" -Wait -WindowStyle Hidden -PassThru >> "data\print-pdf.ps1"
echo         if ^($printJob.ExitCode -eq 0^) { >> "data\print-pdf.ps1"
echo             Write-Host "Impression réussie" >> "data\print-pdf.ps1"
echo         } else { >> "data\print-pdf.ps1"
echo             Write-Host "Erreur impression" >> "data\print-pdf.ps1"
echo         } >> "data\print-pdf.ps1"
echo     } >> "data\print-pdf.ps1"
echo } catch { >> "data\print-pdf.ps1"
echo     Write-Host "Erreur: $_" >> "data\print-pdf.ps1"
echo } >> "data\print-pdf.ps1"

echo ✓ Script PowerShell créé

echo.
echo ========================================
echo Vérification des outils...
echo ========================================

if exist "data\SumatraPDF.exe" (
    echo ✓ SumatraPDF 64-bit disponible
    "data\SumatraPDF.exe" -h 2>nul
) else (
    echo ⚠ SumatraPDF manquant
)

if exist "data\print-pdf.ps1" (
    echo ✓ Script PowerShell d'impression disponible
) else (
    echo ⚠ Script PowerShell manquant
)

echo.
echo ========================================
echo Téléchargement terminé
echo ========================================
echo.
echo OUTILS 64-BIT DISPONIBLES:
echo ✓ SumatraPDF 64-bit (impression PDF)
echo ✓ Script PowerShell (impression native)
echo ✓ Compatible Windows 64-bit moderne
echo.
pause
