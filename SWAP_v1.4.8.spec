# -*- mode: python ; coding: utf-8 -*-
# SWAP Version 1.4.9 - Correction d'impression intelligente
# Date: 2025-08-26
# Corrections: Logique d'impression HP LaserJet M109-M112 / EPSON ET-2810 / Par défaut (non-POSTEK)

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env', '.'), 
        ('data', 'data'),
        ('escpos', 'escpos'),
        ('init_numpy.py', '.'),
    ],
    hiddenimports=[
        'mysql.connector',
        'pdfkit',
        'win32print',
        'win32api',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'pandas',
        'numpy',
        'tkinter',
        'tkinter.ttk',
        'subprocess',
        'tempfile',
        'shutil',
        'dotenv',
        'init_numpy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SWAP_v1.4.9_IMPRESSION_INTELLIGENTE',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='data/exchange.ico',
)
