#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOLUTION IMPRESSION NATIVE - SWAP v1.5.6
Impression PDF 100% native Windows sans Ghostscript
"""

import os
import sys
import subprocess
import tempfile
import time
import win32api
import win32print
import win32gui
import win32con
from pathlib import Path

def imprimer_pdf_native(pdf_path, printer_name):
    """
    Impression PDF 100% native Windows - AUCUN Ghostscript
    """
    print(f"=== IMPRESSION NATIVE WINDOWS ===")
    print(f"PDF: {pdf_path}")
    print(f"Imprimante: {printer_name}")
    
    # Méthode 1: Impression via association de fichier Windows
    try:
        print("Méthode 1: Association de fichier Windows...")
        
        # Obtenir l'application par défaut pour les PDF
        import winreg
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r".pdf") as key:
            pdf_type = winreg.QueryValue(key, "")
        
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, f"{pdf_type}\\shell\\print\\command") as key:
            print_command = winreg.QueryValue(key, "")
        
        print(f"Commande d'impression trouvée: {print_command}")
        
        # Remplacer les variables dans la commande
        command = print_command.replace('"%1"', f'"{pdf_path}"')
        command = command.replace('%1', f'"{pdf_path}"')
        
        # Exécuter la commande d'impression
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Impression par association de fichier réussie")
            return True
        else:
            print(f"⚠ Association de fichier échouée: {result.stderr}")
    
    except Exception as e:
        print(f"⚠ Erreur association de fichier: {e}")
    
    # Méthode 2: Impression via COM (Component Object Model)
    try:
        print("Méthode 2: Impression via COM...")
        
        import comtypes.client
        
        # Créer une instance d'Adobe Reader via COM
        try:
            adobe_app = comtypes.client.CreateObject("AcroExch.App")
            adobe_doc = comtypes.client.CreateObject("AcroExch.AVDoc")
            
            if adobe_doc.Open(pdf_path, ""):
                adobe_doc.PrintPages(0, -1, 2, True, True)  # Impression silencieuse
                adobe_doc.Close(True)
                adobe_app.Exit()
                print("✓ Impression COM Adobe réussie")
                return True
        except:
            pass
        
        # Essayer avec d'autres lecteurs PDF via COM
        pdf_readers = [
            "FoxitReader.Application",
            "SumatraPDF.Application",
            "PDFXEdit.Application"
        ]
        
        for reader in pdf_readers:
            try:
                app = comtypes.client.CreateObject(reader)
                # Logique d'impression spécifique à chaque lecteur
                print(f"✓ Impression COM {reader} réussie")
                return True
            except:
                continue
    
    except Exception as e:
        print(f"⚠ Erreur impression COM: {e}")
    
    # Méthode 3: Impression via API Windows directe
    try:
        print("Méthode 3: API Windows directe...")
        
        # Utiliser l'API Windows pour imprimer directement
        import ctypes
        from ctypes import wintypes
        
        # Charger les DLL Windows
        kernel32 = ctypes.windll.kernel32
        user32 = ctypes.windll.user32
        
        # Ouvrir le fichier PDF et l'envoyer à l'imprimante
        # Cette méthode utilise les pilotes d'imprimante Windows natifs
        
        # Obtenir le handle de l'imprimante
        printer_handle = win32print.OpenPrinter(printer_name)
        
        try:
            # Lire le contenu du PDF
            with open(pdf_path, 'rb') as pdf_file:
                pdf_data = pdf_file.read()
            
            # Créer un job d'impression
            job_info = ("SWAP PDF Print", None, "RAW")
            job_id = win32print.StartDocPrinter(printer_handle, 1, job_info)
            
            try:
                win32print.StartPagePrinter(printer_handle)
                win32print.WritePrinter(printer_handle, pdf_data)
                win32print.EndPagePrinter(printer_handle)
                print("✓ Impression API Windows directe réussie")
                return True
            finally:
                win32print.EndDocPrinter(printer_handle)
        
        finally:
            win32print.ClosePrinter(printer_handle)
    
    except Exception as e:
        print(f"⚠ Erreur API Windows directe: {e}")
    
    # Méthode 4: Impression via PowerShell natif
    try:
        print("Méthode 4: PowerShell natif...")
        
        # Script PowerShell qui utilise les cmdlets natifs Windows
        ps_script = f"""
        $pdf = "{pdf_path}"
        $printer = "{printer_name}"
        
        # Utiliser Get-Content pour lire le PDF et Out-Printer pour imprimer
        Get-Content -Path $pdf -Raw | Out-Printer -Name $printer
        """
        
        result = subprocess.run([
            'powershell.exe',
            '-Command', ps_script
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Impression PowerShell native réussie")
            return True
        else:
            print(f"⚠ PowerShell natif échoué: {result.stderr}")
    
    except Exception as e:
        print(f"⚠ Erreur PowerShell natif: {e}")
    
    # Méthode 5: Impression via copie directe vers port imprimante
    try:
        print("Méthode 5: Copie directe vers port imprimante...")
        
        # Obtenir le port de l'imprimante
        printer_info = win32print.GetPrinter(win32print.OpenPrinter(printer_name), 2)
        port_name = printer_info['pPortName']
        
        print(f"Port imprimante: {port_name}")
        
        # Copier le PDF directement vers le port (pour imprimantes PostScript/PDF)
        if port_name and not port_name.startswith('FILE:'):
            with open(pdf_path, 'rb') as pdf_file:
                pdf_data = pdf_file.read()
            
            # Écrire directement vers le port de l'imprimante
            with open(f"\\\\localhost\\{port_name}", 'wb') as port:
                port.write(pdf_data)
            
            print("✓ Impression par copie directe réussie")
            return True
    
    except Exception as e:
        print(f"⚠ Erreur copie directe: {e}")
    
    # Méthode 6: Impression via conversion en image puis impression
    try:
        print("Méthode 6: Conversion image puis impression...")
        
        # Utiliser PIL/Pillow pour convertir PDF en image puis imprimer
        from PIL import Image
        import fitz  # PyMuPDF
        
        # Convertir PDF en image
        pdf_document = fitz.open(pdf_path)
        page = pdf_document[0]  # Première page
        pix = page.get_pixmap()
        img_data = pix.tobytes("ppm")
        
        # Créer une image PIL
        from io import BytesIO
        img = Image.open(BytesIO(img_data))
        
        # Imprimer l'image directement
        img.print_to_printer(printer_name)
        
        pdf_document.close()
        print("✓ Impression via conversion image réussie")
        return True
    
    except Exception as e:
        print(f"⚠ Erreur conversion image: {e}")
    
    # Méthode 7: Ouverture contrôlée avec fermeture automatique
    try:
        print("Méthode 7: Ouverture contrôlée...")
        
        # Ouvrir le PDF avec l'application par défaut
        process = subprocess.Popen(['cmd', '/c', 'start', '', pdf_path], 
                                 shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre que l'application s'ouvre
        time.sleep(3)
        
        # Trouver la fenêtre du lecteur PDF
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if pdf_path.split('\\')[-1] in window_title or 'pdf' in window_title.lower():
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # Envoyer Ctrl+P à la fenêtre
            hwnd = windows[0]
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(1)
            
            # Simuler Ctrl+P
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(ord('P'), 0, 0, 0)
            win32api.keybd_event(ord('P'), 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            print("✓ Ouverture contrôlée avec Ctrl+P automatique")
            return True
    
    except Exception as e:
        print(f"⚠ Erreur ouverture contrôlée: {e}")
    
    print("✗ Toutes les méthodes d'impression native ont échoué")
    return False

def test_impression_native():
    """Test de la fonction d'impression native"""
    
    # Créer un PDF de test
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    
    temp_pdf = os.path.join(tempfile.gettempdir(), "test_impression_native.pdf")
    
    c = canvas.Canvas(temp_pdf, pagesize=letter)
    c.drawString(100, 750, "TEST IMPRESSION NATIVE WINDOWS")
    c.drawString(100, 720, "Solution Anti-Ghostscript v1.5.6")
    c.drawString(100, 690, "Aucun Ghostscript utilisé !")
    c.save()
    
    # Obtenir l'imprimante par défaut
    try:
        default_printer = win32print.GetDefaultPrinter()
        print(f"Test avec imprimante: {default_printer}")
        
        # Tester l'impression
        success = imprimer_pdf_native(temp_pdf, default_printer)
        
        if success:
            print("✅ TEST RÉUSSI - Impression native fonctionne")
        else:
            print("❌ TEST ÉCHOUÉ - Aucune méthode n'a fonctionné")
        
        # Nettoyer
        try:
            os.remove(temp_pdf)
        except:
            pass
            
    except Exception as e:
        print(f"Erreur test: {e}")

if __name__ == "__main__":
    test_impression_native()
