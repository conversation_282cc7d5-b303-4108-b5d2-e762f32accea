('D:\\SWAP\\build\\SWAP_v1.5.0\\SWAP_v1.5.4_IMPRESSION_MANUELLE.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\SWAP\\build\\SWAP_v1.5.0\\PYZ-00.pyz', 'PYZ'),
  ('struct', 'D:\\SWAP\\build\\SWAP_v1.5.0\\localpycs\\struct.pyc', 'PYMODULE'),
  ('pyimod01_archive',
   'D:\\SWAP\\build\\SWAP_v1.5.0\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\SWAP\\build\\SWAP_v1.5.0\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\SWAP\\build\\SWAP_v1.5.0\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\SWAP\\build\\SWAP_v1.5.0\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('app', 'D:\\SWAP\\app.py', 'PYSOURCE'),
  ('SumatraPDF.exe', 'D:\\SWAP\\SumatraPDF.exe', 'BINARY'),
  ('data\\Acrobat.dll', 'D:\\SWAP\\data\\Acrobat.dll', 'BINARY'),
  ('data\\Acrobat.exe', 'D:\\SWAP\\data\\Acrobat.exe', 'BINARY'),
  ('data\\bin\\gsdll64.dll', 'D:\\SWAP\\data\\bin\\gsdll64.dll', 'BINARY'),
  ('data\\bin\\gswin64.exe', 'D:\\SWAP\\data\\bin\\gswin64.exe', 'BINARY'),
  ('data\\bin\\gswin64c.exe', 'D:\\SWAP\\data\\bin\\gswin64c.exe', 'BINARY'),
  ('data\\bin\\wkhtmltoimage.exe',
   'D:\\SWAP\\data\\bin\\wkhtmltoimage.exe',
   'BINARY'),
  ('data\\bin\\wkhtmltopdf.exe',
   'D:\\SWAP\\data\\bin\\wkhtmltopdf.exe',
   'BINARY'),
  ('data\\bin\\wkhtmltox.dll', 'D:\\SWAP\\data\\bin\\wkhtmltox.dll', 'BINARY'),
  ('data\\gsdll32.dll', 'D:\\SWAP\\data\\gsdll32.dll', 'BINARY'),
  ('data\\gsdll32_00.dll', 'D:\\SWAP\\data\\gsdll32_00.dll', 'BINARY'),
  ('data\\gsprint.exe', 'D:\\SWAP\\data\\gsprint.exe', 'BINARY'),
  ('data\\gswin32.exe', 'D:\\SWAP\\data\\gswin32.exe', 'BINARY'),
  ('data\\gswin32_.exe', 'D:\\SWAP\\data\\gswin32_.exe', 'BINARY'),
  ('data\\wkhtmltoimage.exe', 'D:\\SWAP\\data\\wkhtmltoimage.exe', 'BINARY'),
  ('data\\wkhtmltopdf.exe', 'D:\\SWAP\\data\\wkhtmltopdf.exe', 'BINARY'),
  ('data\\wkhtmltopdf\\bin\\wkhtmltoimage.exe',
   'D:\\SWAP\\data\\wkhtmltopdf\\bin\\wkhtmltoimage.exe',
   'BINARY'),
  ('data\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
   'D:\\SWAP\\data\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
   'BINARY'),
  ('data\\wkhtmltopdf\\bin\\wkhtmltox.dll',
   'D:\\SWAP\\data\\wkhtmltopdf\\bin\\wkhtmltox.dll',
   'BINARY'),
  ('data\\wkhtmltopdf\\uninstall.exe',
   'D:\\SWAP\\data\\wkhtmltopdf\\uninstall.exe',
   'BINARY'),
  ('data\\wkhtmltox.dll', 'D:\\SWAP\\data\\wkhtmltox.dll', 'BINARY'),
  ('python38.dll',
   'C:\\Program Files (x86)\\Python38-32\\python38.dll',
   'BINARY'),
  ('numpy\\.libs\\libopenblas_v0.3.21-gcc_8_3_0.dll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\.libs\\libopenblas_v0.3.21-gcc_8_3_0.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win32.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_philox.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_generator.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\random\\_common.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win32.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_imagingtk.cp38-win32.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_webp.cp38-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_imagingcms.cp38-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_imagingmath.cp38-win32.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\_cffi_backend.cp38-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\PIL\\_imaging.cp38-win32.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\win32print.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\win32\\win32print.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win32.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\markupsafe\\_speedups.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\etree.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\_elementpath.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\sax.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\objectify.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\html\\diff.cp38-win32.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\builder.cp38-win32.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\util.cp38-win32.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp38-win32.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp38-win32.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp38-win32.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp38-win32.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\greenlet\\_greenlet.cp38-win32.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('nacl\\_sodium.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\nacl\\_sodium.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\json.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\join.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\index.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win32.pyd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win32.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files (x86)\\Python38-32\\VCRUNTIME140.dll',
   'BINARY'),
  ('MSVCP140_CODECVT_IDS.dll',
   'C:\\Windows\\system32\\MSVCP140_CODECVT_IDS.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program Files (x86)\\Python38-32\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes38.dll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pywin32_system32\\pywintypes38.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Program Files (x86)\\Python38-32\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('.env', 'D:\\SWAP\\.env', 'DATA'),
  ('data\\bin\\gsdll64.lib', 'D:\\SWAP\\data\\bin\\gsdll64.lib', 'DATA'),
  ('data\\bin\\libwkhtmltox.a', 'D:\\SWAP\\data\\bin\\libwkhtmltox.a', 'DATA'),
  ('data\\capabilities.json', 'D:\\SWAP\\data\\capabilities.json', 'DATA'),
  ('data\\exchange.ico', 'D:\\SWAP\\data\\exchange.ico', 'DATA'),
  ('data\\exchange.png', 'D:\\SWAP\\data\\exchange.png', 'DATA'),
  ('data\\gsdll32.lib', 'D:\\SWAP\\data\\gsdll32.lib', 'DATA'),
  ('data\\libwkhtmltox.a', 'D:\\SWAP\\data\\libwkhtmltox.a', 'DATA'),
  ('data\\swap-icon.png', 'D:\\SWAP\\data\\swap-icon.png', 'DATA'),
  ('data\\swap.png', 'D:\\SWAP\\data\\swap.png', 'DATA'),
  ('data\\temp_print_file\\temp.pdf',
   'D:\\SWAP\\data\\temp_print_file\\temp.pdf',
   'DATA'),
  ('data\\temps - Copie.html', 'D:\\SWAP\\data\\temps - Copie.html', 'DATA'),
  ('data\\temps1.html', 'D:\\SWAP\\data\\temps1.html', 'DATA'),
  ('data\\temps_Portugal.html', 'D:\\SWAP\\data\\temps_Portugal.html', 'DATA'),
  ('data\\temps_Spain.html', 'D:\\SWAP\\data\\temps_Spain.html', 'DATA'),
  ('data\\wkhtmltopdf\\include\\wkhtmltox\\dllbegin.inc',
   'D:\\SWAP\\data\\wkhtmltopdf\\include\\wkhtmltox\\dllbegin.inc',
   'DATA'),
  ('data\\wkhtmltopdf\\include\\wkhtmltox\\dllend.inc',
   'D:\\SWAP\\data\\wkhtmltopdf\\include\\wkhtmltox\\dllend.inc',
   'DATA'),
  ('data\\wkhtmltopdf\\include\\wkhtmltox\\image.h',
   'D:\\SWAP\\data\\wkhtmltopdf\\include\\wkhtmltox\\image.h',
   'DATA'),
  ('data\\wkhtmltopdf\\include\\wkhtmltox\\pdf.h',
   'D:\\SWAP\\data\\wkhtmltopdf\\include\\wkhtmltox\\pdf.h',
   'DATA'),
  ('data\\wkhtmltopdf\\lib\\wkhtmltox.lib',
   'D:\\SWAP\\data\\wkhtmltopdf\\lib\\wkhtmltox.lib',
   'DATA'),
  ('escpos\\capabilities\\capabilities.json',
   'D:\\SWAP\\escpos\\capabilities\\capabilities.json',
   'DATA'),
  ('init_numpy.py', 'D:\\SWAP\\init_numpy.py', 'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\lorem ipsum.txt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('nacl\\py.typed',
   'C:\\Program Files (x86)\\Python38-32\\lib\\site-packages\\nacl\\py.typed',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Program Files '
   '(x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Program Files (x86)\\Python38-32\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Program Files '
   '(x86)\\Python38-32\\lib\\site-packages\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'D:\\SWAP\\build\\SWAP_v1.5.0\\base_library.zip',
   'DATA')],
 'python38.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
