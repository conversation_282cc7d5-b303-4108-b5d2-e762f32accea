#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMPRESSION SIMPLE NATIVE - Solution Anti-Ghostscript
Méthodes d'impression qui évitent complètement Ghostscript
"""

import os
import sys
import subprocess
import time
import win32api
import win32print
import win32gui
import win32con

def imprimer_pdf_simple(pdf_path, printer_name):
    """
    Impression PDF simple sans Ghostscript
    Utilise uniquement les méthodes Windows natives les plus fiables
    """
    print(f"Impression PDF: {pdf_path} vers {printer_name}")
    
    # Méthode 1: win32api.ShellExecute avec "printto" (le plus fiable)
    try:
        print("Tentative 1: win32api ShellExecute printto...")
        win32api.ShellExecute(0, "printto", pdf_path, f'"{printer_name}"', ".", 0)
        print("✓ Impression win32api réussie")
        return True
    except Exception as e:
        print(f"⚠ win32api échoué: {e}")
    
    # Méthode 2: Commande Windows print (native)
    try:
        print("Tentative 2: Commande Windows print...")
        result = subprocess.run([
            'cmd', '/c', 'print', f'/D:{printer_name}', pdf_path
        ], capture_output=True, text=True, timeout=30, 
          creationflags=subprocess.CREATE_NO_WINDOW)
        
        if result.returncode == 0:
            print("✓ Impression commande Windows réussie")
            return True
        else:
            print(f"⚠ Commande Windows échouée: {result.stderr}")
    except Exception as e:
        print(f"⚠ Commande Windows erreur: {e}")
    
    # Méthode 3: Copie vers imprimante réseau
    try:
        print("Tentative 3: Copie vers imprimante réseau...")
        printer_path = f"\\\\localhost\\{printer_name}"
        
        # Copier le fichier vers l'imprimante
        result = subprocess.run([
            'copy', pdf_path, printer_path
        ], shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Impression par copie réussie")
            return True
        else:
            print(f"⚠ Copie vers imprimante échouée: {result.stderr}")
    except Exception as e:
        print(f"⚠ Copie vers imprimante erreur: {e}")
    
    # Méthode 4: Ouverture simple (sans "print" pour éviter Ghostscript)
    try:
        print("Tentative 4: Ouverture simple du PDF...")
        os.startfile(pdf_path)
        print("✓ PDF ouvert - Impression manuelle requise (Ctrl+P)")
        return True
    except Exception as e:
        print(f"⚠ Ouverture PDF échouée: {e}")
    
    print("✗ Toutes les méthodes ont échoué")
    return False

def imprimer_avec_adobe_reader(pdf_path, printer_name):
    """
    Impression spécifique avec Adobe Reader (évite Ghostscript)
    """
    adobe_paths = [
        r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
        r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
        r"C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
        r"C:\Program Files (x86)\Adobe\Reader 11.0\Reader\AcroRd32.exe"
    ]
    
    for adobe_path in adobe_paths:
        if os.path.exists(adobe_path):
            try:
                print(f"Impression avec Adobe Reader: {adobe_path}")
                
                # Utiliser le paramètre /t pour impression silencieuse
                result = subprocess.run([
                    adobe_path,
                    '/t',  # Impression silencieuse
                    pdf_path,
                    printer_name
                ], timeout=30, creationflags=subprocess.CREATE_NO_WINDOW)
                
                print("✓ Impression Adobe Reader lancée")
                return True
                
            except Exception as e:
                print(f"⚠ Adobe Reader échoué: {e}")
                continue
    
    print("⚠ Adobe Reader non trouvé")
    return False

def imprimer_avec_edge(pdf_path, printer_name):
    """
    Impression avec Microsoft Edge (lecteur PDF intégré Windows 10/11)
    """
    try:
        print("Impression avec Microsoft Edge...")
        
        # Edge peut imprimer des PDF directement
        edge_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        
        if os.path.exists(edge_path):
            # Ouvrir le PDF avec Edge et déclencher l'impression
            result = subprocess.run([
                edge_path,
                '--print-to-pdf',
                f'--printer={printer_name}',
                pdf_path
            ], timeout=30, creationflags=subprocess.CREATE_NO_WINDOW)
            
            print("✓ Impression Edge lancée")
            return True
        else:
            print("⚠ Microsoft Edge non trouvé")
            return False
            
    except Exception as e:
        print(f"⚠ Edge impression échouée: {e}")
        return False

def impression_automatique_complete(pdf_path, printer_name):
    """
    Fonction principale d'impression automatique
    Teste toutes les méthodes dans l'ordre de fiabilité
    """
    print("=== IMPRESSION AUTOMATIQUE ANTI-GHOSTSCRIPT ===")
    print(f"PDF: {pdf_path}")
    print(f"Imprimante: {printer_name}")
    print("=" * 50)
    
    # Vérifier que le fichier existe
    if not os.path.exists(pdf_path):
        print(f"✗ Erreur: Fichier PDF non trouvé: {pdf_path}")
        return False
    
    # Vérifier que l'imprimante existe
    try:
        printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
        if printer_name not in printers:
            print(f"⚠ Imprimante non trouvée: {printer_name}")
            print(f"Imprimantes disponibles: {printers}")
            # Utiliser l'imprimante par défaut si celle spécifiée n'existe pas
            printer_name = win32print.GetDefaultPrinter()
            print(f"Utilisation imprimante par défaut: {printer_name}")
    except Exception as e:
        print(f"⚠ Erreur vérification imprimante: {e}")
    
    # Essayer les différentes méthodes
    methods = [
        ("Adobe Reader", lambda: imprimer_avec_adobe_reader(pdf_path, printer_name)),
        ("Microsoft Edge", lambda: imprimer_avec_edge(pdf_path, printer_name)),
        ("Méthodes natives", lambda: imprimer_pdf_simple(pdf_path, printer_name))
    ]
    
    for method_name, method_func in methods:
        print(f"\n--- Essai: {method_name} ---")
        try:
            if method_func():
                print(f"✅ SUCCÈS avec {method_name}")
                return True
        except Exception as e:
            print(f"✗ {method_name} a échoué: {e}")
        
        # Petite pause entre les tentatives
        time.sleep(1)
    
    print("\n❌ ÉCHEC: Toutes les méthodes d'impression ont échoué")
    print("Le PDF a été créé mais l'impression automatique n'a pas fonctionné")
    return False

# Test de la fonction
if __name__ == "__main__":
    import tempfile
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    
    # Créer un PDF de test
    temp_pdf = os.path.join(tempfile.gettempdir(), "test_impression_simple.pdf")
    
    c = canvas.Canvas(temp_pdf, pagesize=letter)
    c.drawString(100, 750, "TEST IMPRESSION SIMPLE NATIVE")
    c.drawString(100, 720, "Solution Anti-Ghostscript SWAP v1.5.6")
    c.drawString(100, 690, "Aucun Ghostscript ne devrait apparaître !")
    c.save()
    
    # Obtenir l'imprimante par défaut
    try:
        default_printer = win32print.GetDefaultPrinter()
        print(f"Test avec imprimante: {default_printer}")
        
        # Tester l'impression
        success = impression_automatique_complete(temp_pdf, default_printer)
        
        if success:
            print("\n✅ TEST GLOBAL RÉUSSI")
        else:
            print("\n❌ TEST GLOBAL ÉCHOUÉ")
        
        # Nettoyer
        try:
            os.remove(temp_pdf)
        except:
            pass
            
    except Exception as e:
        print(f"Erreur test global: {e}")
