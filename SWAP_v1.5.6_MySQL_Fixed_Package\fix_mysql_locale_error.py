#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIX MYSQL LOCALE ERROR - SWAP v1.5.6
Corrige l'erreur "No localization support for language 'eng'"
"""

import os
import sys
import locale

def fix_mysql_locale():
    """
    Corriger l'erreur de localisation MySQL
    """
    print("=== CORRECTION ERREUR MYSQL LOCALE ===")
    
    # Méthode 1: Forcer la locale en anglais US
    try:
        print("Tentative 1: Configuration locale en-US...")
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        print("✓ Locale configurée: en_US.UTF-8")
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'English_United States.1252')
            print("✓ Locale configurée: English_United States.1252")
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'C')
                print("✓ Locale configurée: C (par défaut)")
            except Exception as e:
                print(f"⚠ Impossible de configurer la locale: {e}")
    
    # Méthode 2: Variables d'environnement
    print("\nTentative 2: Configuration variables d'environnement...")
    os.environ['LC_ALL'] = 'en_US.UTF-8'
    os.environ['LANG'] = 'en_US.UTF-8'
    os.environ['LANGUAGE'] = 'en_US:en'
    print("✓ Variables d'environnement configurées")
    
    # Méthode 3: Configuration MySQL spécifique
    print("\nTentative 3: Configuration MySQL...")
    os.environ['MYSQL_LOCALE'] = 'en_US'
    os.environ['MYSQL_LANG'] = 'en'
    print("✓ Variables MySQL configurées")
    
    print("\n✅ Correction de la locale terminée")

def test_mysql_connection():
    """
    Tester la connexion MySQL après correction
    """
    print("\n=== TEST CONNEXION MYSQL ===")
    
    try:
        import mysql.connector
        print("✓ Module mysql.connector importé avec succès")
        
        # Configuration de test (remplacez par vos vraies valeurs)
        config = {
            'host': '***************',  # Remplacez par votre host
            'database': 'warehousedb',   # Remplacez par votre base
            'user': 'nwt02',       # Remplacez par votre utilisateur
            'password': 'twn02',       # Remplacez par votre mot de passe
            'port': 3306,         # Remplacez par votre port
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True
        }
        
        print("Tentative de connexion MySQL...")
        conn = mysql.connector.connect(**config)
        print("✅ Connexion MySQL réussie !")
        conn.close()
        print("✓ Connexion fermée proprement")
        
    except mysql.connector.Error as err:
        print(f"❌ Erreur MySQL: {err}")
        return False
    except ImportError as err:
        print(f"❌ Erreur d'import: {err}")
        return False
    except Exception as err:
        print(f"❌ Erreur générale: {err}")
        return False
    
    return True

def create_mysql_config_fix():
    """
    Créer un fichier de configuration MySQL corrigé
    """
    print("\n=== CRÉATION CONFIGURATION MYSQL ===")
    
    config_content = '''
# Configuration MySQL pour SWAP - Fix Locale Error
import os
import locale
import mysql.connector

# Correction de la locale avant import MySQL
def setup_mysql_locale():
    """Configurer la locale pour MySQL"""
    try:
        # Forcer la locale en anglais
        locale.setlocale(locale.LC_ALL, 'C')
        os.environ['LC_ALL'] = 'C'
        os.environ['LANG'] = 'C'
        os.environ['LANGUAGE'] = 'en'
        return True
    except:
        return False

# Configuration de connexion MySQL avec gestion d'erreur
def create_mysql_connection(host, database, user, password, port=3306):
    """Créer une connexion MySQL avec gestion d'erreur de locale"""
    
    # Appliquer le fix de locale
    setup_mysql_locale()
    
    try:
        config = {
            'host': host,
            'database': database,
            'user': user,
            'password': password,
            'port': port,
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True,
            'raise_on_warnings': False
        }
        
        conn = mysql.connector.connect(**config)
        return conn
        
    except mysql.connector.Error as err:
        print(f"Erreur MySQL: {err}")
        return None
    except Exception as err:
        print(f"Erreur générale: {err}")
        return None

# Exemple d'utilisation:
# conn = create_mysql_connection('localhost', 'database', 'user', 'password')
'''
    
    with open('mysql_config_fixed.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ Fichier mysql_config_fixed.py créé")

def patch_app_py():
    """
    Créer un patch pour app.py
    """
    print("\n=== CRÉATION PATCH APP.PY ===")
    
    patch_content = '''
# PATCH MYSQL LOCALE - À ajouter au début de app.py

import os
import locale

# FIX MYSQL LOCALE ERROR
def fix_mysql_locale():
    """Corriger l'erreur de locale MySQL"""
    try:
        # Méthode 1: Locale C (universelle)
        locale.setlocale(locale.LC_ALL, 'C')
        os.environ['LC_ALL'] = 'C'
        os.environ['LANG'] = 'C'
        os.environ['LANGUAGE'] = 'en'
        return True
    except:
        try:
            # Méthode 2: Locale anglaise
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            os.environ['LC_ALL'] = 'en_US.UTF-8'
            os.environ['LANG'] = 'en_US.UTF-8'
            return True
        except:
            return False

# Appliquer le fix avant tout import MySQL
fix_mysql_locale()

# Maintenant importer mysql.connector
import mysql.connector

# Reste du code app.py...
'''
    
    with open('mysql_locale_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("✓ Fichier mysql_locale_patch.py créé")

def main():
    """Fonction principale"""
    print("🔧 CORRECTION ERREUR MYSQL LOCALE - SWAP v1.5.6")
    print("=" * 50)
    
    # Appliquer les corrections
    fix_mysql_locale()
    
    # Créer les fichiers de configuration
    create_mysql_config_fix()
    patch_app_py()
    
    # Tester la connexion
    success = test_mysql_connection()
    
    print("\n" + "=" * 50)
    print("RÉSUMÉ DE LA CORRECTION")
    print("=" * 50)
    
    if success:
        print("✅ CORRECTION RÉUSSIE")
        print("✓ Locale configurée correctement")
        print("✓ Connexion MySQL testée")
        print("✓ Fichiers de configuration créés")
    else:
        print("⚠ CORRECTION PARTIELLE")
        print("✓ Locale configurée")
        print("✓ Fichiers de configuration créés")
        print("⚠ Test de connexion échoué (vérifiez les paramètres)")
    
    print("\nFICHIERS CRÉÉS:")
    print("- mysql_config_fixed.py (configuration corrigée)")
    print("- mysql_locale_patch.py (patch pour app.py)")
    
    print("\nINSTRUCTIONS:")
    print("1. Ajoutez le contenu de mysql_locale_patch.py au début de app.py")
    print("2. Ou utilisez mysql_config_fixed.py pour vos connexions")
    print("3. Recompilez SWAP avec ces corrections")
    
    print("\n🎯 L'erreur 'No localization support for language eng' devrait être résolue !")

if __name__ == "__main__":
    main()
