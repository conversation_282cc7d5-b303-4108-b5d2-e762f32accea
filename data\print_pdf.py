# Script Python pour impression PDF 
import sys 
import os 
import subprocess 
import win32api 
import win32print 
 
def print_pdf(pdf_path, printer_name): 
    """Imprimer un PDF sans déclencher Ghostscript""" 
    print(f"Impression: {pdf_path} vers {printer_name}") 
 
    # Méthode 1: Utiliser win32api avec printto 
    try: 
        win32api.ShellExecute(0, "printto", pdf_path, f'"{printer_name}"', ".", 0) 
        print("Impression win32api réussie") 
        return True 
    except Exception as e: 
        print(f"Erreur win32api: {e}") 
 
    # Méthode 2: Ouvrir le PDF (sans print) 
    try: 
        os.startfile(pdf_path) 
        print("PDF ouvert pour impression manuelle") 
        return True 
    except Exception as e: 
        print(f"Erreur ouverture: {e}") 
        return False 
 
if __name__ == "__main__": 
    if len(sys.argv) != 3: 
        print("Usage: python print_pdf.py fichier.pdf nom_imprimante") 
        sys.exit(1) 
 
    pdf_path = sys.argv[1] 
    printer_name = sys.argv[2] 
    print_pdf(pdf_path, printer_name) 
