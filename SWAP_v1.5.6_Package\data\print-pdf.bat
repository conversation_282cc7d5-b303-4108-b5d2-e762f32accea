@echo off 
REM Script batch pour impression PDF 
set PDF_FILE= 
set PRINTER_NAME= 
 
echo Impression PDF: %PDF_FILE% vers %PRINTER_NAME% 
 
REM Méthode 1: Utiliser la commande print de Windows 
print /D:%PRINTER_NAME% "%PDF_FILE%" 
if %ERRORLEVEL% EQU 0 ( 
    echo Impression Windows réussie 
    exit /b 0 
) 
 
REM Méthode 2: Copie vers l'imprimante 
copy "%PDF_FILE%" "\\localhost\%PRINTER_NAME%" 
if %ERRORLEVEL% EQU 0 ( 
    echo Impression par copie réussie 
    exit /b 0 
) 
 
echo Erreur: Impossible d'imprimer 
exit /b 1 
