#!/usr/bin/env python3
"""
Script de test pour simuler différents scénarios d'imprimantes
"""

def simulate_printer_selection(available_printers, default_printer):
    """Simule la logique de sélection d'imprimante avec des données de test"""
    
    print(f"Imprimante par défaut simulée: {default_printer}")
    print(f"Imprimantes disponibles simulées: {available_printers}")
    
    # Logique de sélection d'imprimante pour PDF principal
    printer_name = None
    
    # 1. Chercher HP LaserJet M109-M112
    for printer in available_printers:
        if "HP LaserJet M109-M112" in printer:
            printer_name = printer
            print(f'✓ HP LaserJet M109-M112 trouvée: {printer_name}')
            break
    
    # 2. Si pas trouvée, chercher EPSON avec 2810
    if printer_name is None:
        for printer in available_printers:
            if "EPSON" in printer and "2810" in printer:
                printer_name = printer
                print(f'✓ EPSON 2810 trouvée: {printer_name}')
                break
    
    # 3. Si aucune des deux n'est trouvée, utiliser l'imprimante par défaut (sauf si c'est POSTEK)
    if printer_name is None:
        if default_printer and "POSTEK" not in default_printer:
            printer_name = default_printer
            print(f'✓ Utilisation de l\'imprimante par défaut: {printer_name}')
        else:
            # Si l'imprimante par défaut est POSTEK, chercher une autre imprimante
            for printer in available_printers:
                if "POSTEK" not in printer:
                    printer_name = printer
                    print(f'✓ Imprimante par défaut est POSTEK, utilisation alternative: {printer_name}')
                    break
    
    # Si aucune imprimante appropriée n'est trouvée, utiliser la première non-POSTEK
    if printer_name is None:
        for printer in available_printers:
            if "POSTEK" not in printer:
                printer_name = printer
                print(f'✓ Utilisation de la première imprimante non-POSTEK: {printer_name}')
                break
    
    if printer_name is None:
        print("❌ Aucune imprimante appropriée trouvée!")
    else:
        print(f"🎯 Imprimante sélectionnée pour le PDF: {printer_name}")
    
    # Vérifier les imprimantes POSTEK pour outbound
    postek_printers = [printer for printer in available_printers if "POSTEK" in printer]
    if postek_printers:
        print(f"📄 Imprimantes POSTEK disponibles pour outbound: {postek_printers}")
    else:
        print("📄 Aucune imprimante POSTEK trouvée pour outbound")
    
    return printer_name

def test_scenarios():
    """Test différents scénarios"""
    
    print("=== Scénario 1: HP LaserJet M109-M112 disponible ===")
    printers1 = ['OneNote for Windows 10', 'POSTEK C168/200s', 'HP LaserJet M109-M112', 'Microsoft Print to PDF']
    default1 = 'POSTEK C168/200s'
    simulate_printer_selection(printers1, default1)
    
    print("\n=== Scénario 2: Seulement EPSON ET-2810 disponible ===")
    printers2 = ['OneNote for Windows 10', 'POSTEK C168/200s', 'EPSON ET-2810 Series', 'Microsoft Print to PDF']
    default2 = 'POSTEK C168/200s'
    simulate_printer_selection(printers2, default2)
    
    print("\n=== Scénario 3: Ni HP ni EPSON 2810, imprimante par défaut non-POSTEK ===")
    printers3 = ['OneNote for Windows 10', 'POSTEK C168/200s', 'Canon Printer', 'Microsoft Print to PDF']
    default3 = 'Canon Printer'
    simulate_printer_selection(printers3, default3)
    
    print("\n=== Scénario 4: Seulement POSTEK et autres ===")
    printers4 = ['OneNote for Windows 10', 'POSTEK C168/200s', 'Microsoft Print to PDF']
    default4 = 'POSTEK C168/200s'
    simulate_printer_selection(printers4, default4)
    
    print("\n=== Scénario 5: HP et EPSON 2810 disponibles (HP doit être prioritaire) ===")
    printers5 = ['EPSON ET-2810 Series', 'POSTEK C168/200s', 'HP LaserJet M109-M112', 'Microsoft Print to PDF']
    default5 = 'POSTEK C168/200s'
    simulate_printer_selection(printers5, default5)

if __name__ == "__main__":
    test_scenarios()
