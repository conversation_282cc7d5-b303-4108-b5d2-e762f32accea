@echo off 
echo ======================================== 
echo SWAP v1.5.6 - Vérification du système 
echo ======================================== 
echo. 
echo Vérification des imprimantes disponibles... 
python -c "import win32print; printers = [p[2] for p in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]; print('Imprimantes disponibles:'); [print(f'  - {p}') for p in printers]; print(f'\\nImprimante par défaut: {win32print.GetDefaultPrinter()}')" 
echo. 
echo Vérification des outils PDF... 
if exist "data\print-pdf.ps1" ( 
    echo ✓ Script PowerShell d'impression disponible 
) else ( 
    echo ⚠ Script PowerShell manquant 
) 
if exist "data\SumatraPDF.exe" ( 
    echo ✓ SumatraPDF disponible 
) else ( 
    echo ⚠ SumatraPDF manquant 
) 
echo. 
echo Vérification terminée. 
pause 
