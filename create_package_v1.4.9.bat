@echo off
echo ========================================
echo SWAP Version 1.4.9 - Package Creator
echo Correction d'impression intelligente
echo ========================================

set PACKAGE_NAME=SWAP_v1.4.9_Package
set EXE_NAME=SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe

echo.
echo Creation du package %PACKAGE_NAME%...

REM Verifier que l'executable existe
if not exist "dist\%EXE_NAME%" (
    echo ERREUR: L'executable n'existe pas!
    echo Veuillez d'abord executer build_v1.4.9.bat
    pause
    exit /b 1
)

REM Creer le dossier du package
if exist "%PACKAGE_NAME%" rmdir /s /q "%PACKAGE_NAME%"
mkdir "%PACKAGE_NAME%"

echo.
echo Copie des fichiers...

REM Copier l'executable principal
copy "dist\%EXE_NAME%" "%PACKAGE_NAME%\" >nul
echo - Executable principal copie ✓

REM Copier le dossier data complet
xcopy "data" "%PACKAGE_NAME%\data\" /E /I /Q >nul
echo - Dossier data copie ✓

REM Copier SumatraPDF si il existe
if exist "SumatraPDF.exe" (
    copy "SumatraPDF.exe" "%PACKAGE_NAME%\" >nul
    echo - SumatraPDF copie ✓
)

REM Copier le fichier .env si il existe
if exist ".env" (
    copy ".env" "%PACKAGE_NAME%\" >nul
    echo - Fichier .env copie ✓
)

REM Creer un script de lancement
echo @echo off > "%PACKAGE_NAME%\run_SWAP_v1.4.9.bat"
echo echo Lancement de SWAP v1.4.9... >> "%PACKAGE_NAME%\run_SWAP_v1.4.9.bat"
echo start "" "%EXE_NAME%" >> "%PACKAGE_NAME%\run_SWAP_v1.4.9.bat"
echo - Script de lancement cree ✓

REM Creer un script de verification
echo @echo off > "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo ======================================== >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo SWAP v1.4.9 - Verification du systeme >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo ======================================== >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo. >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo Verification des imprimantes... >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo wmic printer list brief >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo. >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo Imprimante par defaut: >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo wmic printer where default=true get name >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo. >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo echo Appuyez sur une touche pour continuer... >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo pause ^>nul >> "%PACKAGE_NAME%\VERIFICATION_v1.4.9.bat"
echo - Script de verification cree ✓

REM Creer un fichier d'information sur la version
echo SWAP Version 1.4.9 - Correction d'impression intelligente > "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo Date de creation: %date% %time% >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo. >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo CORRECTIONS APPORTEES: >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo ====================== >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo. >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 1. Logique d'impression intelligente pour les PDF: >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo    - Priorite 1: HP LaserJet M109-M112 >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo    - Priorite 2: EPSON contenant "2810" >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo    - Priorite 3: Imprimante par defaut (si non-POSTEK) >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo    - Priorite 4: Premiere imprimante non-POSTEK >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo. >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 2. POSTEK reserve uniquement aux etiquettes outbound >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo. >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 3. Aucune autre fonctionnalite modifiee >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo. >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo INSTALLATION: >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo ============= >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 1. Copier tout le dossier sur le PC cible >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 2. Executer VERIFICATION_v1.4.9.bat pour verifier les imprimantes >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo 3. Lancer l'application avec run_SWAP_v1.4.9.bat >> "%PACKAGE_NAME%\VERSION_1.4.9_INFO.txt"
echo - Fichier d'information cree ✓

REM Creer un fichier README pour l'installation
echo SWAP v1.4.9 - GUIDE D'INSTALLATION > "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo ===================================== >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo ETAPES D'INSTALLATION: >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo 1. Copier tout le dossier SWAP_v1.4.9_Package sur le PC cible >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo 2. Verifier les imprimantes disponibles: >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo    - Double-cliquer sur VERIFICATION_v1.4.9.bat >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo    - Verifier que les imprimantes HP LaserJet M109-M112 ou EPSON ET-2810 sont presentes >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo 3. Configurer la base de donnees: >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo    - Modifier le fichier .env avec les parametres de votre base de donnees >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo 4. Lancer l'application: >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo    - Double-cliquer sur run_SWAP_v1.4.9.bat >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo    - OU double-cliquer directement sur %EXE_NAME% >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo. >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo NOUVEAUTES v1.4.9: >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo =================== >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo - Correction du probleme d'impression >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo - Selection intelligente d'imprimante pour les PDF >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo - POSTEK reserve aux etiquettes outbound uniquement >> "%PACKAGE_NAME%\README_INSTALLATION.txt"
echo - Fichier README d'installation cree ✓

echo.
echo Calcul de la taille du package...
for /f "tokens=3" %%a in ('dir "%PACKAGE_NAME%" /-c ^| find "File(s)"') do set size=%%a
echo Taille du package: %size% bytes

echo.
echo ========================================
echo Package cree avec succes! ✓
echo ========================================
echo.
echo Contenu du package:
dir "%PACKAGE_NAME%" /B
echo.
echo Le package se trouve dans: %PACKAGE_NAME%\
echo.
echo Pour deployer sur un autre PC:
echo 1. Copier tout le dossier %PACKAGE_NAME%
echo 2. Executer VERIFICATION_v1.4.9.bat sur le PC cible
echo 3. Lancer run_SWAP_v1.4.9.bat
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
