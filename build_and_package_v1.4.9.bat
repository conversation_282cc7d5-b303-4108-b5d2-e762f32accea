@echo off
echo ========================================
echo SWAP Version 1.4.9 - Build et Package
echo Correction d'impression intelligente
echo ========================================

echo.
echo Etape 1/2: Compilation de l'executable...
echo ========================================

call build_v1.4.9.bat

if errorlevel 1 (
    echo.
    echo ERREUR: La compilation a echoue!
    pause
    exit /b 1
)

echo.
echo Etape 2/2: Creation du package...
echo ========================================

call create_package_v1.4.9.bat

if errorlevel 1 (
    echo.
    echo ERREUR: La creation du package a echoue!
    pause
    exit /b 1
)

echo.
echo ========================================
echo PROCESSUS TERMINE AVEC SUCCES! ✓
echo ========================================
echo.
echo L'executable et le package sont prets pour le deploiement.
echo.
echo Fichiers crees:
echo - dist\SWAP_v1.4.9_IMPRESSION_INTELLIGENTE.exe
echo - SWAP_v1.4.9_Package\ (dossier complet)
echo.
echo Pour deployer sur un autre PC:
echo 1. Copier le dossier SWAP_v1.4.9_Package
echo 2. Executer VERIFICATION_v1.4.9.bat sur le PC cible
echo 3. Lancer run_SWAP_v1.4.9.bat
echo.
echo Appuyez sur une touche pour terminer...
pause >nul
