#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST IMPRESSION ANTI-GHOSTSCRIPT
Vérifie que l'impression fonctionne sans déclencher Ghostscript
"""

import os
import sys
import subprocess
import tempfile
import time
import win32api
import win32print
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_pdf():
    """Créer un PDF de test"""
    temp_dir = tempfile.gettempdir()
    pdf_path = os.path.join(temp_dir, "test_anti_ghostscript.pdf")
    
    c = canvas.Canvas(pdf_path, pagesize=letter)
    c.drawString(100, 750, "TEST IMPRESSION ANTI-GHOSTSCRIPT")
    c.drawString(100, 720, "SWAP v1.5.6 - Solution Définitive")
    c.drawString(100, 690, "=" * 50)
    c.drawString(100, 660, "✓ Adobe Reader (priorité 1)")
    c.drawString(100, 630, "✓ win32api printto (fallback)")
    c.drawString(100, 600, "✓ Commande Windows native")
    c.drawString(100, 570, "✓ Ouverture manuelle (dernier recours)")
    c.drawString(100, 540, "=" * 50)
    c.drawString(100, 510, "AUCUN GHOSTSCRIPT NE DEVRAIT APPARAÎTRE !")
    c.drawString(100, 480, "Si vous voyez cette page imprimée,")
    c.drawString(100, 450, "la solution anti-Ghostscript fonctionne !")
    c.save()
    
    print(f"✓ PDF de test créé: {pdf_path}")
    return pdf_path

def test_adobe_reader(pdf_path, printer_name):
    """Tester l'impression avec Adobe Reader"""
    print("\n=== TEST ADOBE READER ===")
    
    adobe_paths = [
        r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
        r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
        r"C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
        r"C:\Program Files (x86)\Adobe\Reader 11.0\Reader\AcroRd32.exe"
    ]
    
    for adobe_path in adobe_paths:
        if os.path.exists(adobe_path):
            print(f"✓ Adobe Reader trouvé: {adobe_path}")
            try:
                print(f"Impression vers: {printer_name}")
                result = subprocess.run([
                    adobe_path,
                    '/t',  # Impression silencieuse
                    pdf_path,
                    printer_name
                ], timeout=30, creationflags=subprocess.CREATE_NO_WINDOW)
                
                print("✅ IMPRESSION ADOBE READER LANCÉE")
                return True
                
            except Exception as e:
                print(f"❌ Erreur Adobe Reader: {e}")
                continue
    
    print("⚠ Adobe Reader non trouvé")
    return False

def test_win32api_printto(pdf_path, printer_name):
    """Tester l'impression avec win32api printto"""
    print("\n=== TEST WIN32API PRINTTO ===")
    
    try:
        print(f"Impression vers: {printer_name}")
        win32api.ShellExecute(0, "printto", pdf_path, f'"{printer_name}"', ".", 0)
        print("✅ IMPRESSION WIN32API RÉUSSIE")
        return True
    except Exception as e:
        print(f"❌ Erreur win32api: {e}")
        return False

def test_windows_print_command(pdf_path, printer_name):
    """Tester l'impression avec la commande Windows print"""
    print("\n=== TEST COMMANDE WINDOWS PRINT ===")
    
    try:
        print(f"Impression vers: {printer_name}")
        result = subprocess.run([
            'cmd', '/c', 'print', f'/D:{printer_name}', pdf_path
        ], capture_output=True, text=True, timeout=30, 
          creationflags=subprocess.CREATE_NO_WINDOW)
        
        if result.returncode == 0:
            print("✅ IMPRESSION COMMANDE WINDOWS RÉUSSIE")
            return True
        else:
            print(f"❌ Erreur commande Windows: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erreur commande Windows: {e}")
        return False

def test_manual_opening(pdf_path):
    """Tester l'ouverture manuelle"""
    print("\n=== TEST OUVERTURE MANUELLE ===")
    
    try:
        print("Ouverture du PDF...")
        os.startfile(pdf_path)
        print("✅ PDF OUVERT - Utilisez Ctrl+P pour imprimer")
        return True
    except Exception as e:
        print(f"❌ Erreur ouverture: {e}")
        return False

def monitor_ghostscript():
    """Surveiller si Ghostscript se lance"""
    print("\n=== SURVEILLANCE GHOSTSCRIPT ===")
    
    # Vérifier les processus en cours
    try:
        result = subprocess.run([
            'tasklist', '/FI', 'IMAGENAME eq gswin*'
        ], capture_output=True, text=True)
        
        if 'gswin' in result.stdout:
            print("❌ GHOSTSCRIPT DÉTECTÉ EN COURS D'EXÉCUTION !")
            print(result.stdout)
            return True
        else:
            print("✅ Aucun processus Ghostscript détecté")
            return False
    except Exception as e:
        print(f"⚠ Erreur surveillance: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=" * 60)
    print("TEST IMPRESSION ANTI-GHOSTSCRIPT - SWAP v1.5.6")
    print("=" * 60)
    
    # Créer un PDF de test
    pdf_path = create_test_pdf()
    
    # Obtenir l'imprimante par défaut
    try:
        printer_name = win32print.GetDefaultPrinter()
        print(f"✓ Imprimante par défaut: {printer_name}")
    except Exception as e:
        print(f"❌ Impossible d'obtenir l'imprimante par défaut: {e}")
        return
    
    # Surveillance initiale de Ghostscript
    print("\n--- Surveillance initiale ---")
    ghostscript_initial = monitor_ghostscript()
    
    # Tests des différentes méthodes
    success_count = 0
    total_tests = 4
    
    print("\n" + "=" * 60)
    print("DÉBUT DES TESTS D'IMPRESSION")
    print("=" * 60)
    
    # Test 1: Adobe Reader
    if test_adobe_reader(pdf_path, printer_name):
        success_count += 1
        time.sleep(3)  # Attendre que l'impression se lance
        
        # Vérifier si Ghostscript s'est lancé
        if monitor_ghostscript():
            print("⚠ ATTENTION: Ghostscript détecté après Adobe Reader")
        else:
            print("✅ Adobe Reader: Aucun Ghostscript détecté")
    
    # Test 2: win32api printto
    if test_win32api_printto(pdf_path, printer_name):
        success_count += 1
        time.sleep(3)
        
        if monitor_ghostscript():
            print("⚠ ATTENTION: Ghostscript détecté après win32api")
        else:
            print("✅ win32api: Aucun Ghostscript détecté")
    
    # Test 3: Commande Windows print
    if test_windows_print_command(pdf_path, printer_name):
        success_count += 1
        time.sleep(3)
        
        if monitor_ghostscript():
            print("⚠ ATTENTION: Ghostscript détecté après commande Windows")
        else:
            print("✅ Commande Windows: Aucun Ghostscript détecté")
    
    # Test 4: Ouverture manuelle
    if test_manual_opening(pdf_path):
        success_count += 1
        time.sleep(3)
        
        if monitor_ghostscript():
            print("⚠ ATTENTION: Ghostscript détecté après ouverture manuelle")
        else:
            print("✅ Ouverture manuelle: Aucun Ghostscript détecté")
    
    # Résultats finaux
    print("\n" + "=" * 60)
    print("RÉSULTATS DES TESTS")
    print("=" * 60)
    print(f"Tests réussis: {success_count}/{total_tests}")
    
    # Surveillance finale de Ghostscript
    print("\n--- Surveillance finale ---")
    ghostscript_final = monitor_ghostscript()
    
    if success_count > 0:
        print("✅ AU MOINS UNE MÉTHODE FONCTIONNE")
        if not ghostscript_final:
            print("🎉 SUCCÈS TOTAL: Impression sans Ghostscript !")
        else:
            print("⚠ ATTENTION: Ghostscript s'est lancé malgré les précautions")
    else:
        print("❌ AUCUNE MÉTHODE N'A FONCTIONNÉ")
    
    # Instructions pour l'utilisateur
    print("\n" + "=" * 60)
    print("INSTRUCTIONS")
    print("=" * 60)
    print("1. Vérifiez votre imprimante pour confirmer l'impression")
    print("2. Si Ghostscript apparaît, fermez-le immédiatement")
    print("3. La solution privilégie Adobe Reader puis win32api")
    print("4. En dernier recours, le PDF s'ouvre pour impression manuelle")
    
    # Nettoyage
    try:
        os.remove(pdf_path)
        print(f"\n✓ PDF de test supprimé: {pdf_path}")
    except:
        pass
    
    print("\n" + "=" * 60)
    print("TEST TERMINÉ")
    print("=" * 60)

if __name__ == "__main__":
    main()
