@echo off
echo ========================================
echo SWAP v1.5.0 - Installation sur PC cible
echo Correction d'impression intelligente
echo ========================================

echo.
echo Verification du systeme...

:: Verifier si nous sommes dans le bon dossier
if not exist "SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe" (
    echo ERREUR: Executable non trouve!
    echo Assurez-vous d'etre dans le dossier SWAP_v1.5.0_Package
    pause
    exit /b 1
)

echo ✓ Executable trouve

:: Verifier les fichiers necessaires
echo.
echo Verification des fichiers...

if exist "SumatraPDF.exe" (
    echo ✓ SumatraPDF.exe
) else (
    echo ✗ SumatraPDF.exe manquant
)

if exist "data" (
    echo ✓ Dossier data
) else (
    echo ✗ Dossier data manquant
)

if exist ".env" (
    echo ✓ Fichier .env
) else (
    echo ! Fichier .env manquant (sera cree)
)

echo.
echo Verification des imprimantes...
echo Imprimantes detectees:
wmic printer get name | findstr /v "Name"

echo.
echo Recherche des imprimantes specifiques...

:: Chercher HP LaserJet M109-M112
wmic printer get name | findstr /i "HP LaserJet M109-M112" >nul
if not errorlevel 1 (
    echo ✓ HP LaserJet M109-M112 detectee
) else (
    echo ! HP LaserJet M109-M112 non detectee
)

:: Chercher EPSON ET-2810
wmic printer get name | findstr /i "EPSON" | findstr /i "2810" >nul
if not errorlevel 1 (
    echo ✓ EPSON ET-2810 detectee
) else (
    echo ! EPSON ET-2810 non detectee
)

:: Chercher POSTEK
wmic printer get name | findstr /i "POSTEK" >nul
if not errorlevel 1 (
    echo ✓ POSTEK detectee (pour etiquettes outbound)
) else (
    echo ! POSTEK non detectee
)

echo.
echo Creation du fichier .env si necessaire...
if not exist ".env" (
    echo Creation du fichier .env avec valeurs par defaut...
    (
    echo # Configuration SWAP v1.5.0
    echo DB_HOST=localhost
    echo DB_PORT=3306
    echo DB_USER=root
    echo DB_PASSWORD=
    echo DB_NAME=swap_db
    ) > .env
    echo ✓ Fichier .env cree
    echo ATTENTION: Modifiez le fichier .env avec vos parametres de base de donnees
)

echo.
echo Creation du raccourci sur le bureau...
set DESKTOP=%USERPROFILE%\Desktop
set CURRENT_DIR=%CD%
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\SWAP v1.5.0.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "SWAP v1.5.0 - Correction impression intelligente" >> CreateShortcut.vbs
echo oLink.IconLocation = "%CURRENT_DIR%\data\exchange.ico" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs >nul
del CreateShortcut.vbs
echo ✓ Raccourci cree sur le bureau

echo.
echo Test de lancement...
echo Voulez-vous tester l'application maintenant? (O/N)
set /p choice=
if /i "%choice%"=="O" (
    echo Lancement de SWAP v1.5.0...
    start "" "SWAP_v1.5.0_IMPRESSION_INTELLIGENTE.exe"
)

echo.
echo ========================================
echo Installation terminee!
echo ========================================
echo.
echo RESUME:
echo - Application installee dans: %CD%
echo - Raccourci cree sur le bureau
echo - Fichier .env configure
echo.
echo PROCHAINES ETAPES:
echo 1. Modifiez le fichier .env avec vos parametres DB
echo 2. Verifiez que les imprimantes sont bien configurees
echo 3. Lancez l'application via le raccourci ou run_SWAP_v1.5.0.bat
echo.
echo En cas de probleme:
echo - Executez VERIFICATION_v1.5.0.bat
echo - Verifiez les logs dans debug.log
echo.
pause
