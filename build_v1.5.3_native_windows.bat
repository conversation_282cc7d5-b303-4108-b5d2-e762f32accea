@echo off
echo ========================================
echo SWAP v1.5.3 - Build IMPRESSION NATIVE WINDOWS
echo ========================================
echo.
echo CORRECTIONS APPORTÉES:
echo ✓ Impression native Windows (sans SumatraPDF)
echo ✓ Plus d'interface Ghostscript
echo ✓ Évite les problèmes de SumatraPDF corrompu
echo ✓ Méthodes d'impression multiples avec fallback
echo ✓ Logique d'impression intelligente maintenue
echo.

REM Vérifier si PyInstaller est installé
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo Installation de PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "build" rmdir /s /q "build"
if exist "dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe" del "dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe"

REM Créer l'exécutable
echo.
echo Création de l'exécutable SWAP v1.5.3...
echo.
pyinstaller SWAP_v1.5.0.spec

REM Vérifier si la compilation a réussi
if exist "dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe" (
    echo.
    echo ========================================
    echo ✓ BUILD RÉUSSI !
    echo ========================================
    echo.
    echo Exécutable créé: dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe
    echo Taille du fichier:
    dir "dist\SWAP_v1.5.3_IMPRESSION_NATIVE_WINDOWS.exe" | find ".exe"
    echo.
    
    REM Créer le package de déploiement
    call create_package_v1.5.3.bat
    
) else (
    echo.
    echo ========================================
    echo ✗ ÉCHEC DU BUILD
    echo ========================================
    echo.
    echo Vérifiez les erreurs ci-dessus
)

echo.
pause
