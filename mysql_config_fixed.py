
# Configuration MySQL pour SWAP - Fix Locale Error
import os
import locale
import mysql.connector

# Correction de la locale avant import MySQL
def setup_mysql_locale():
    """Configurer la locale pour MySQL"""
    try:
        # Forcer la locale en anglais
        locale.setlocale(locale.LC_ALL, 'C')
        os.environ['LC_ALL'] = 'C'
        os.environ['LANG'] = 'C'
        os.environ['LANGUAGE'] = 'en'
        return True
    except:
        return False

# Configuration de connexion MySQL avec gestion d'erreur
def create_mysql_connection(host, database, user, password, port=3306):
    """Créer une connexion MySQL avec gestion d'erreur de locale"""
    
    # Appliquer le fix de locale
    setup_mysql_locale()
    
    try:
        config = {
            'host': host,
            'database': database,
            'user': user,
            'password': password,
            'port': port,
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True,
            'raise_on_warnings': False
        }
        
        conn = mysql.connector.connect(**config)
        return conn
        
    except mysql.connector.Error as err:
        print(f"Erreur MySQL: {err}")
        return None
    except Exception as err:
        print(f"Erreur générale: {err}")
        return None

# Exemple d'utilisation:
# conn = create_mysql_connection('localhost', 'database', 'user', 'password')
