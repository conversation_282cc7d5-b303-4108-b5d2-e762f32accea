#!/usr/bin/env python3
"""
Script de test pour vérifier la logique de sélection d'imprimante
"""

import win32print

def test_printer_selection():
    """Test de la logique de sélection d'imprimante"""
    
    # Obtenir l'imprimante par défaut
    try:
        default_printer = win32print.GetDefaultPrinter()
        print(f"Imprimante par défaut: {default_printer}")
    except Exception as e:
        print(f"Erreur lors de l'obtention de l'imprimante par défaut: {e}")
        default_printer = None
    
    # Obtenir la liste des imprimantes disponibles
    try:
        available_printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
        print(f"Imprimantes disponibles: {available_printers}")
    except Exception as e:
        print(f"Erreur lors de l'énumération des imprimantes: {e}")
        available_printers = []
    
    # Logique de sélection d'imprimante pour PDF principal
    printer_name = None
    
    # 1. Chercher HP LaserJet M109-M112
    for printer in available_printers:
        if "HP LaserJet M109-M112" in printer:
            printer_name = printer
            print(f'✓ HP LaserJet M109-M112 trouvée: {printer_name}')
            break
    
    # 2. Si pas trouvée, chercher EPSON avec 2810
    if printer_name is None:
        for printer in available_printers:
            if "EPSON" in printer and "2810" in printer:
                printer_name = printer
                print(f'✓ EPSON 2810 trouvée: {printer_name}')
                break
    
    # 3. Si aucune des deux n'est trouvée, utiliser l'imprimante par défaut (sauf si c'est POSTEK)
    if printer_name is None:
        if default_printer and "POSTEK" not in default_printer:
            printer_name = default_printer
            print(f'✓ Utilisation de l\'imprimante par défaut: {printer_name}')
        else:
            # Si l'imprimante par défaut est POSTEK, chercher une autre imprimante
            for printer in available_printers:
                if "POSTEK" not in printer:
                    printer_name = printer
                    print(f'✓ Imprimante par défaut est POSTEK, utilisation alternative: {printer_name}')
                    break
    
    # Si aucune imprimante appropriée n'est trouvée, utiliser la première non-POSTEK
    if printer_name is None:
        for printer in available_printers:
            if "POSTEK" not in printer:
                printer_name = printer
                print(f'✓ Utilisation de la première imprimante non-POSTEK: {printer_name}')
                break
    
    if printer_name is None:
        print("❌ Aucune imprimante appropriée trouvée!")
    else:
        print(f"🎯 Imprimante sélectionnée pour le PDF: {printer_name}")
    
    # Vérifier les imprimantes POSTEK pour outbound
    postek_printers = [printer for printer in available_printers if "POSTEK" in printer]
    if postek_printers:
        print(f"📄 Imprimantes POSTEK disponibles pour outbound: {postek_printers}")
    else:
        print("📄 Aucune imprimante POSTEK trouvée pour outbound")
    
    return printer_name

if __name__ == "__main__":
    print("=== Test de la logique de sélection d'imprimante ===")
    test_printer_selection()
