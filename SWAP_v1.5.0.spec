# -*- mode: python ; coding: utf-8 -*-
# SWAP Version 1.5.6 - IMPRESSION AUTOMATIQUE 64-BIT
# Date: 2025-08-26
# Corrections:
# - Logique d'impression HP LaserJet M109-M112 / EPSON ET-2810 / Par d<PERSON> (non-POSTEK)
# - POSTEK réservé uniquement aux étiquettes outbound
# - IMPRESSION AUTOMATIQUE avec SumatraPDF 64-bit et PowerShell
# - Évite complètement Ghostscript avec outils 64-bit
# - Compatible Windows 64-bit moderne

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env', '.'), 
        ('data', 'data'),
        ('escpos', 'escpos'),
        ('init_numpy.py', '.'),
        ('SumatraPDF.exe', '.'),
    ],
    hiddenimports=[
        'mysql.connector',
        'mysql.connector.pooling',
        'mysql.connector.constants',
        'pdfkit',
        'win32print',
        'win32api',
        'win32gui',
        'win32con',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'pandas',
        'numpy',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'subprocess',
        'tempfile',
        'shutil',
        'dotenv',
        'init_numpy',
        'os',
        'sys',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SWAP_v1.5.6_IMPRESSION_AUTOMATIQUE_64BIT',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='data/exchange.ico',
    version_file=None,
)
